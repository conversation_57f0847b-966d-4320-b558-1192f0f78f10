// Case Detail Screen

import React, { useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Linking,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import { acceptCase, rejectCase, startVerification } from '../../store/slices/casesSlice';
import { CasesStackParamList } from '../../navigation/MainTabNavigator';
import { COLORS, SPACING, BORDER_RADIUS, CASE_STATUS_LABELS, VERIFICATION_TYPE_LABELS } from '../../constants';

type NavigationProp = StackNavigationProp<CasesStackParamList, 'CaseDetail'>;
type RouteProp_ = RouteProp<CasesStackParamList, 'CaseDetail'>;

const CaseDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp_>();
  const { caseId } = route.params;
  
  const dispatch = useDispatch<AppDispatch>();
  const { selectedCase } = useSelector((state: RootState) => state.cases);
  const { status: syncStatus } = useSelector((state: RootState) => state.sync);

  useEffect(() => {
    if (!selectedCase) {
      navigation.goBack();
    }
  }, [selectedCase, navigation]);

  if (!selectedCase) {
    return null;
  }

  const handleAcceptCase = () => {
    Alert.alert(
      'Accept Case',
      'Are you sure you want to accept this verification case?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Accept',
          onPress: async () => {
            try {
              await dispatch(acceptCase(selectedCase.id)).unwrap();
              Alert.alert('Success', 'Case accepted successfully');
            } catch (error) {
              Alert.alert('Error', error as string);
            }
          },
        },
      ]
    );
  };

  const handleRejectCase = () => {
    Alert.alert(
      'Reject Case',
      'Are you sure you want to reject this verification case? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reject',
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(rejectCase(selectedCase.id)).unwrap();
              Alert.alert('Success', 'Case rejected successfully');
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', error as string);
            }
          },
        },
      ]
    );
  };

  const handleStartVerification = () => {
    Alert.alert(
      'Start Verification',
      'This will mark the case as in progress and allow you to begin the verification process.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start',
          onPress: async () => {
            try {
              await dispatch(startVerification(selectedCase.id)).unwrap();
              // Navigate to verification screen
              navigation.navigate('Verification' as any, { caseId: selectedCase.id });
            } catch (error) {
              Alert.alert('Error', error as string);
            }
          },
        },
      ]
    );
  };

  const handleCallApplicant = () => {
    if (selectedCase.applicantPhone) {
      Linking.openURL(`tel:${selectedCase.applicantPhone}`);
    } else {
      Alert.alert('No Phone Number', 'Phone number not available for this applicant');
    }
  };

  const handleOpenMaps = () => {
    if (selectedCase.coordinates) {
      const { latitude, longitude } = selectedCase.coordinates;
      const url = `https://maps.google.com/?q=${latitude},${longitude}`;
      Linking.openURL(url);
    } else {
      Alert.alert('No Location', 'GPS coordinates not available for this case');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned': return COLORS.WARNING;
      case 'accepted': return COLORS.PRIMARY;
      case 'in_progress': return COLORS.SECONDARY;
      case 'completed': return COLORS.SUCCESS;
      case 'rejected': return COLORS.ERROR;
      case 'synced': return COLORS.SUCCESS;
      default: return COLORS.TEXT_SECONDARY;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return COLORS.ERROR;
      case 'medium': return COLORS.WARNING;
      case 'low': return COLORS.SUCCESS;
      default: return COLORS.TEXT_SECONDARY;
    }
  };

  const canAcceptReject = selectedCase.status === 'assigned';
  const canStartVerification = selectedCase.status === 'accepted';
  const canContinueVerification = selectedCase.status === 'in_progress';

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header Card */}
      <View style={styles.headerCard}>
        <View style={styles.headerTop}>
          <View style={styles.caseInfo}>
            <Text style={styles.caseNumber}>{selectedCase.caseNumber}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedCase.status) }]}>
              <Text style={styles.statusText}>{CASE_STATUS_LABELS[selectedCase.status]}</Text>
            </View>
          </View>
          <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(selectedCase.priority) }]} />
        </View>
        
        <Text style={styles.verificationType}>
          {VERIFICATION_TYPE_LABELS[selectedCase.verificationType]}
        </Text>
        
        <View style={styles.syncStatus}>
          <Ionicons 
            name={syncStatus.isOnline ? "wifi" : "wifi-off"} 
            size={16} 
            color={syncStatus.isOnline ? COLORS.SUCCESS : COLORS.ERROR} 
          />
          <Text style={styles.syncStatusText}>
            {syncStatus.isOnline ? 'Online' : 'Offline Mode'}
          </Text>
        </View>
      </View>

      {/* Applicant Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Applicant Information</Text>
        <View style={styles.infoRow}>
          <Ionicons name="person-outline" size={20} color={COLORS.PRIMARY} />
          <Text style={styles.infoText}>{selectedCase.applicantName}</Text>
        </View>
        {selectedCase.applicantPhone && (
          <TouchableOpacity style={styles.infoRow} onPress={handleCallApplicant}>
            <Ionicons name="call-outline" size={20} color={COLORS.PRIMARY} />
            <Text style={[styles.infoText, styles.linkText]}>{selectedCase.applicantPhone}</Text>
            <Ionicons name="open-outline" size={16} color={COLORS.PRIMARY} />
          </TouchableOpacity>
        )}
      </View>

      {/* Address Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Address</Text>
        <TouchableOpacity style={styles.addressContainer} onPress={handleOpenMaps}>
          <View style={styles.addressRow}>
            <Ionicons name="location-outline" size={20} color={COLORS.PRIMARY} />
            <Text style={[styles.infoText, styles.addressText]}>{selectedCase.address}</Text>
          </View>
          {selectedCase.coordinates && (
            <View style={styles.coordinatesRow}>
              <Text style={styles.coordinatesText}>
                {selectedCase.coordinates.latitude.toFixed(6)}, {selectedCase.coordinates.longitude.toFixed(6)}
              </Text>
              <Ionicons name="open-outline" size={16} color={COLORS.PRIMARY} />
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Case Details */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Case Details</Text>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Created:</Text>
          <Text style={styles.detailValue}>{new Date(selectedCase.createdAt).toLocaleDateString()}</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Due Date:</Text>
          <Text style={styles.detailValue}>{new Date(selectedCase.dueDate).toLocaleDateString()}</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Priority:</Text>
          <View style={styles.priorityContainer}>
            <View style={[styles.priorityDot, { backgroundColor: getPriorityColor(selectedCase.priority) }]} />
            <Text style={[styles.detailValue, { textTransform: 'capitalize' }]}>{selectedCase.priority}</Text>
          </View>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Assigned To:</Text>
          <Text style={styles.detailValue}>{selectedCase.assignedTo}</Text>
        </View>
      </View>

      {/* Notes */}
      {selectedCase.notes && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notes</Text>
          <Text style={styles.notesText}>{selectedCase.notes}</Text>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.actionsContainer}>
        {canAcceptReject && (
          <View style={styles.actionRow}>
            <TouchableOpacity style={styles.rejectButton} onPress={handleRejectCase}>
              <Ionicons name="close-circle-outline" size={20} color={COLORS.SURFACE} />
              <Text style={styles.rejectButtonText}>Reject</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.acceptButton} onPress={handleAcceptCase}>
              <Ionicons name="checkmark-circle-outline" size={20} color={COLORS.SURFACE} />
              <Text style={styles.acceptButtonText}>Accept</Text>
            </TouchableOpacity>
          </View>
        )}
        
        {canStartVerification && (
          <TouchableOpacity style={styles.startButton} onPress={handleStartVerification}>
            <Ionicons name="play-circle-outline" size={20} color={COLORS.SURFACE} />
            <Text style={styles.startButtonText}>Start Verification</Text>
          </TouchableOpacity>
        )}
        
        {canContinueVerification && (
          <TouchableOpacity style={styles.continueButton} onPress={handleStartVerification}>
            <Ionicons name="arrow-forward-circle-outline" size={20} color={COLORS.SURFACE} />
            <Text style={styles.continueButtonText}>Continue Verification</Text>
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  headerCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    padding: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.SM,
  },
  caseInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  caseNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginRight: SPACING.SM,
  },
  statusBadge: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: BORDER_RADIUS.SM,
  },
  statusText: {
    fontSize: 12,
    color: COLORS.SURFACE,
    fontWeight: 'bold',
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  verificationType: {
    fontSize: 16,
    color: COLORS.SECONDARY,
    fontWeight: '600',
    marginBottom: SPACING.SM,
  },
  syncStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  syncStatusText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.XS,
  },
  section: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  infoText: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    marginLeft: SPACING.SM,
    flex: 1,
  },
  linkText: {
    color: COLORS.PRIMARY,
  },
  addressContainer: {
    borderRadius: BORDER_RADIUS.SM,
    backgroundColor: COLORS.BACKGROUND,
    padding: SPACING.SM,
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  addressText: {
    flex: 1,
    lineHeight: 20,
  },
  coordinatesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: SPACING.XS,
    paddingTop: SPACING.XS,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  coordinatesText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    fontFamily: 'monospace',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  detailLabel: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '400',
  },
  priorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: SPACING.XS,
  },
  notesText: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
    lineHeight: 20,
  },
  actionsContainer: {
    padding: SPACING.MD,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.MD,
  },
  rejectButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.ERROR,
    paddingVertical: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    marginRight: SPACING.SM,
  },
  rejectButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: SPACING.XS,
  },
  acceptButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.SUCCESS,
    paddingVertical: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    marginLeft: SPACING.SM,
  },
  acceptButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: SPACING.XS,
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
  },
  startButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: SPACING.XS,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.SECONDARY,
    paddingVertical: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
  },
  continueButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: SPACING.XS,
  },
});

export default CaseDetailScreen;
