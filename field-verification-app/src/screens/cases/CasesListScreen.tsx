// Cases List Screen

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Alert,
  TextInput,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import { fetchCases, refreshCases, selectCase } from '../../store/slices/casesSlice';
import { CasesStackParamList } from '../../navigation/MainTabNavigator';
import { VerificationCase } from '../../types';
import { COLORS, SPACING, BORDER_RADIUS, CASE_STATUS_LABELS, VERIFICATION_TYPE_LABELS } from '../../constants';

type NavigationProp = StackNavigationProp<CasesStackParamList, 'CasesList'>;

const CasesListScreen: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  
  const navigation = useNavigation<NavigationProp>();
  const dispatch = useDispatch<AppDispatch>();
  
  const { cases, isLoading, isRefreshing, error, lastSyncTime } = useSelector(
    (state: RootState) => state.cases
  );
  const { status: syncStatus } = useSelector((state: RootState) => state.sync);

  useEffect(() => {
    dispatch(fetchCases());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(refreshCases());
  };

  const handleCasePress = (caseItem: VerificationCase) => {
    dispatch(selectCase(caseItem.id));
    navigation.navigate('CaseDetail', { caseId: caseItem.id });
  };

  const filteredCases = cases.filter(caseItem => {
    const matchesSearch = 
      caseItem.caseNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      caseItem.applicantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      caseItem.address.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = filterStatus === 'all' || caseItem.status === filterStatus;
    
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned': return COLORS.WARNING;
      case 'accepted': return COLORS.PRIMARY;
      case 'in_progress': return COLORS.SECONDARY;
      case 'completed': return COLORS.SUCCESS;
      case 'rejected': return COLORS.ERROR;
      case 'synced': return COLORS.SUCCESS;
      default: return COLORS.TEXT_SECONDARY;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return COLORS.ERROR;
      case 'medium': return COLORS.WARNING;
      case 'low': return COLORS.SUCCESS;
      default: return COLORS.TEXT_SECONDARY;
    }
  };

  const renderCaseItem = ({ item }: { item: VerificationCase }) => (
    <TouchableOpacity style={styles.caseCard} onPress={() => handleCasePress(item)}>
      <View style={styles.caseHeader}>
        <View style={styles.caseInfo}>
          <Text style={styles.caseNumber}>{item.caseNumber}</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusText}>{CASE_STATUS_LABELS[item.status]}</Text>
          </View>
        </View>
        <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(item.priority) }]} />
      </View>
      
      <Text style={styles.applicantName}>{item.applicantName}</Text>
      <Text style={styles.verificationType}>
        {VERIFICATION_TYPE_LABELS[item.verificationType]}
      </Text>
      <Text style={styles.address} numberOfLines={2}>{item.address}</Text>
      
      <View style={styles.caseFooter}>
        <View style={styles.dateInfo}>
          <Ionicons name="calendar-outline" size={14} color={COLORS.TEXT_SECONDARY} />
          <Text style={styles.dateText}>Due: {new Date(item.dueDate).toLocaleDateString()}</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={COLORS.TEXT_SECONDARY} />
      </View>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color={COLORS.TEXT_SECONDARY} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search cases..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      <View style={styles.filterContainer}>
        <Text style={styles.filterLabel}>Filter:</Text>
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'all' && styles.filterButtonActive]}
          onPress={() => setFilterStatus('all')}
        >
          <Text style={[styles.filterButtonText, filterStatus === 'all' && styles.filterButtonTextActive]}>
            All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'assigned' && styles.filterButtonActive]}
          onPress={() => setFilterStatus('assigned')}
        >
          <Text style={[styles.filterButtonText, filterStatus === 'assigned' && styles.filterButtonTextActive]}>
            Assigned
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'in_progress' && styles.filterButtonActive]}
          onPress={() => setFilterStatus('in_progress')}
        >
          <Text style={[styles.filterButtonText, filterStatus === 'in_progress' && styles.filterButtonTextActive]}>
            In Progress
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.statusBar}>
        <View style={styles.statusItem}>
          <Ionicons 
            name={syncStatus.isOnline ? "wifi" : "wifi-off"} 
            size={16} 
            color={syncStatus.isOnline ? COLORS.SUCCESS : COLORS.ERROR} 
          />
          <Text style={styles.statusText}>
            {syncStatus.isOnline ? 'Online' : 'Offline'}
          </Text>
        </View>
        {lastSyncTime && (
          <Text style={styles.lastSyncText}>
            Last sync: {new Date(lastSyncTime).toLocaleTimeString()}
          </Text>
        )}
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="folder-open-outline" size={64} color={COLORS.TEXT_SECONDARY} />
      <Text style={styles.emptyStateTitle}>No Cases Found</Text>
      <Text style={styles.emptyStateText}>
        {searchQuery || filterStatus !== 'all' 
          ? 'Try adjusting your search or filter criteria'
          : 'Pull down to refresh and load your assigned cases'
        }
      </Text>
    </View>
  );

  if (error && cases.length === 0) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={64} color={COLORS.ERROR} />
        <Text style={styles.errorTitle}>Unable to Load Cases</Text>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => dispatch(fetchCases())}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredCases}
        renderItem={renderCaseItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
            tintColor={COLORS.PRIMARY}
          />
        }
        contentContainerStyle={filteredCases.length === 0 ? styles.emptyContainer : undefined}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: BORDER_RADIUS.MD,
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.MD,
  },
  searchIcon: {
    marginRight: SPACING.SM,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  filterLabel: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginRight: SPACING.SM,
  },
  filterButton: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: BORDER_RADIUS.SM,
    backgroundColor: COLORS.BACKGROUND,
    marginRight: SPACING.XS,
  },
  filterButtonActive: {
    backgroundColor: COLORS.PRIMARY,
  },
  filterButtonText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  filterButtonTextActive: {
    color: COLORS.SURFACE,
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.XS,
  },
  lastSyncText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  caseCard: {
    backgroundColor: COLORS.SURFACE,
    marginHorizontal: SPACING.MD,
    marginVertical: SPACING.XS,
    padding: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  caseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.SM,
  },
  caseInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  caseNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginRight: SPACING.SM,
  },
  statusBadge: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: BORDER_RADIUS.SM,
  },
  statusText: {
    fontSize: 10,
    color: COLORS.SURFACE,
    fontWeight: 'bold',
  },
  priorityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  applicantName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  verificationType: {
    fontSize: 14,
    color: COLORS.SECONDARY,
    marginBottom: SPACING.XS,
  },
  address: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SM,
  },
  caseFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.XS,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.XXL,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  emptyStateText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.XXL,
    backgroundColor: COLORS.BACKGROUND,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  errorText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.LG,
  },
  retryButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.LG,
    paddingVertical: SPACING.SM,
    borderRadius: BORDER_RADIUS.MD,
  },
  retryButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CasesListScreen;
