// Sync Screen

import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import {
  checkNetworkStatus,
  getSyncQueueStatus,
  syncData,
  retryFailedSync,
  clearSyncQueue,
} from '../../store/slices/syncSlice';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';

const SyncScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { status, isSyncing, error } = useSelector((state: RootState) => state.sync);

  useEffect(() => {
    dispatch(checkNetworkStatus());
    dispatch(getSyncQueueStatus());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(checkNetworkStatus());
    dispatch(getSyncQueueStatus());
  };

  const handleSyncNow = () => {
    if (!status.isOnline) {
      Alert.alert('No Internet', 'Please check your internet connection and try again.');
      return;
    }
    
    dispatch(syncData());
  };

  const handleRetryFailed = () => {
    if (!status.isOnline) {
      Alert.alert('No Internet', 'Please check your internet connection and try again.');
      return;
    }
    
    dispatch(retryFailedSync());
  };

  const handleClearQueue = () => {
    Alert.alert(
      'Clear Sync Queue',
      'This will permanently delete all pending uploads. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => dispatch(clearSyncQueue()),
        },
      ]
    );
  };

  const getConnectionStatusColor = () => {
    return status.isOnline ? COLORS.SUCCESS : COLORS.ERROR;
  };

  const getConnectionStatusText = () => {
    return status.isOnline ? 'Connected' : 'Offline';
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={false}
          onRefresh={handleRefresh}
          colors={[COLORS.PRIMARY]}
          tintColor={COLORS.PRIMARY}
        />
      }
      showsVerticalScrollIndicator={false}
    >
      {/* Connection Status */}
      <View style={styles.statusCard}>
        <View style={styles.statusHeader}>
          <Ionicons
            name={status.isOnline ? 'wifi' : 'wifi-off'}
            size={24}
            color={getConnectionStatusColor()}
          />
          <Text style={[styles.statusTitle, { color: getConnectionStatusColor() }]}>
            {getConnectionStatusText()}
          </Text>
        </View>
        
        {status.lastSyncTime && (
          <Text style={styles.lastSyncText}>
            Last sync: {new Date(status.lastSyncTime).toLocaleString()}
          </Text>
        )}
      </View>

      {/* Sync Statistics */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <View style={styles.statIcon}>
            <Ionicons name="cloud-upload-outline" size={24} color={COLORS.WARNING} />
          </View>
          <Text style={styles.statNumber}>{status.pendingUploads}</Text>
          <Text style={styles.statLabel}>Pending Uploads</Text>
        </View>
        
        <View style={styles.statCard}>
          <View style={styles.statIcon}>
            <Ionicons name="alert-circle-outline" size={24} color={COLORS.ERROR} />
          </View>
          <Text style={styles.statNumber}>{status.failedUploads}</Text>
          <Text style={styles.statLabel}>Failed Uploads</Text>
        </View>
      </View>

      {/* Sync Actions */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.primaryButton,
            (!status.isOnline || isSyncing) && styles.disabledButton,
          ]}
          onPress={handleSyncNow}
          disabled={!status.isOnline || isSyncing}
        >
          <Ionicons
            name={isSyncing ? 'sync' : 'cloud-upload'}
            size={20}
            color={COLORS.SURFACE}
          />
          <Text style={styles.actionButtonText}>
            {isSyncing ? 'Syncing...' : 'Sync Now'}
          </Text>
        </TouchableOpacity>

        {status.failedUploads > 0 && (
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.warningButton,
              (!status.isOnline || isSyncing) && styles.disabledButton,
            ]}
            onPress={handleRetryFailed}
            disabled={!status.isOnline || isSyncing}
          >
            <Ionicons name="refresh" size={20} color={COLORS.SURFACE} />
            <Text style={styles.actionButtonText}>Retry Failed</Text>
          </TouchableOpacity>
        )}

        {status.pendingUploads > 0 && (
          <TouchableOpacity
            style={[styles.actionButton, styles.dangerButton]}
            onPress={handleClearQueue}
          >
            <Ionicons name="trash-outline" size={20} color={COLORS.SURFACE} />
            <Text style={styles.actionButtonText}>Clear Queue</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Error Display */}
      {error && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={20} color={COLORS.ERROR} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {/* Information */}
      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>About Sync</Text>
        <Text style={styles.infoText}>
          • Data is automatically synced when you have an internet connection
        </Text>
        <Text style={styles.infoText}>
          • You can work offline and sync later when connected
        </Text>
        <Text style={styles.infoText}>
          • Failed uploads will be retried automatically
        </Text>
        <Text style={styles.infoText}>
          • Photos and verification data are stored locally until synced
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  statusCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    padding: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: SPACING.SM,
  },
  lastSyncText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  statsContainer: {
    flexDirection: 'row',
    marginHorizontal: SPACING.MD,
    marginBottom: SPACING.MD,
  },
  statCard: {
    flex: 1,
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    alignItems: 'center',
    marginHorizontal: SPACING.XS,
  },
  statIcon: {
    marginBottom: SPACING.SM,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  actionsContainer: {
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.MD,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    marginBottom: SPACING.SM,
  },
  primaryButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  warningButton: {
    backgroundColor: COLORS.WARNING,
  },
  dangerButton: {
    backgroundColor: COLORS.ERROR,
  },
  disabledButton: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  actionButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: SPACING.SM,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.ERROR,
    marginHorizontal: SPACING.MD,
    padding: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    marginBottom: SPACING.MD,
  },
  errorText: {
    color: COLORS.SURFACE,
    fontSize: 14,
    marginLeft: SPACING.SM,
    flex: 1,
  },
  infoContainer: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    padding: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  infoText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
    marginBottom: SPACING.XS,
  },
});

export default SyncScreen;
