// Camera Screen with Geolocation

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import * as Location from 'expo-location';
import * as MediaLibrary from 'expo-media-library';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import { savePhoto, setCapturingPhoto } from '../../store/slices/verificationSlice';
import { VerificationStackParamList } from '../../navigation/MainTabNavigator';
import { VerificationPhoto, LocationData } from '../../types';
import { COLORS, SPACING, PHOTO_QUALITY, LOCATION_ACCURACY_THRESHOLD } from '../../constants';

type NavigationProp = StackNavigationProp<VerificationStackParamList, 'Camera'>;
type RouteProp_ = RouteProp<VerificationStackParamList, 'Camera'>;

const { width, height } = Dimensions.get('window');

const CameraScreen: React.FC = () => {
  const [facing, setFacing] = useState<CameraType>('back');
  const [isCapturing, setIsCapturing] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [cameraPermission, requestCameraPermission] = useCameraPermissions();
  const [mediaLibraryPermission, setMediaLibraryPermission] = useState<boolean>(false);
  const [locationPermission, setLocationPermission] = useState<boolean>(false);
  
  const cameraRef = useRef<CameraView>(null);
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp_>();
  const { caseId } = route.params;
  
  const dispatch = useDispatch<AppDispatch>();
  const { isCapturingPhoto } = useSelector((state: RootState) => state.verification);

  useEffect(() => {
    checkPermissions();
    getCurrentLocation();
  }, []);

  const checkPermissions = async () => {
    try {
      // Check media library permission
      const mediaLibraryStatus = await MediaLibrary.requestPermissionsAsync();
      setMediaLibraryPermission(mediaLibraryStatus.status === 'granted');
      
      // Check location permission
      const locationStatus = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(locationStatus.status === 'granted');
      
      if (locationStatus.status !== 'granted') {
        setLocationError('Location permission is required for geotagged photos');
      }
    } catch (error) {
      console.error('Permission check failed:', error);
    }
  };

  const getCurrentLocation = async () => {
    try {
      setLocationError(null);
      
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeInterval: 5000,
        distanceInterval: 10,
      });
      
      if (location.coords.accuracy && location.coords.accuracy > LOCATION_ACCURACY_THRESHOLD) {
        setLocationError(`Location accuracy is low (${Math.round(location.coords.accuracy)}m). Please wait for better GPS signal.`);
      }
      
      setCurrentLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || 0,
        timestamp: location.timestamp,
      });
    } catch (error) {
      console.error('Location error:', error);
      setLocationError('Unable to get current location. Please check GPS settings.');
    }
  };

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const capturePhoto = async () => {
    if (!cameraRef.current || isCapturing || isCapturingPhoto) return;
    
    if (!currentLocation) {
      Alert.alert(
        'Location Required',
        'GPS location is required for verification photos. Please wait for location to be acquired.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    if (currentLocation.accuracy > LOCATION_ACCURACY_THRESHOLD) {
      Alert.alert(
        'Poor GPS Signal',
        `Location accuracy is ${Math.round(currentLocation.accuracy)}m. Continue anyway?`,
        [
          { text: 'Wait for Better Signal', style: 'cancel' },
          { text: 'Continue', onPress: () => takePicture() },
        ]
      );
      return;
    }
    
    takePicture();
  };

  const takePicture = async () => {
    try {
      setIsCapturing(true);
      dispatch(setCapturingPhoto(true));
      
      const photo = await cameraRef.current!.takePictureAsync({
        quality: PHOTO_QUALITY,
        base64: false,
        exif: true,
      });
      
      if (!photo || !currentLocation) {
        throw new Error('Failed to capture photo or get location');
      }
      
      // Create verification photo object
      const verificationPhoto: VerificationPhoto = {
        id: `photo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        caseId,
        uri: photo.uri,
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        timestamp: new Date().toISOString(),
        isRequired: true,
        isSynced: false,
      };
      
      // Save photo to local database
      await dispatch(savePhoto(verificationPhoto)).unwrap();
      
      // Save to device gallery if permission granted
      if (mediaLibraryPermission) {
        try {
          await MediaLibrary.saveToLibraryAsync(photo.uri);
        } catch (error) {
          console.warn('Failed to save to gallery:', error);
        }
      }
      
      Alert.alert(
        'Photo Captured',
        'Photo has been saved with location data.',
        [
          { text: 'Take Another', style: 'default' },
          { text: 'Done', onPress: () => navigation.goBack() },
        ]
      );
      
    } catch (error) {
      console.error('Photo capture failed:', error);
      Alert.alert('Error', 'Failed to capture photo. Please try again.');
    } finally {
      setIsCapturing(false);
      dispatch(setCapturingPhoto(false));
    }
  };

  const retryLocation = () => {
    getCurrentLocation();
  };

  if (!cameraPermission) {
    return (
      <View style={styles.permissionContainer}>
        <Ionicons name="camera-outline" size={64} color={COLORS.TEXT_SECONDARY} />
        <Text style={styles.permissionTitle}>Camera Permission Required</Text>
        <Text style={styles.permissionText}>
          This app needs camera access to capture verification photos.
        </Text>
        <TouchableOpacity style={styles.permissionButton} onPress={requestCameraPermission}>
          <Text style={styles.permissionButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!cameraPermission.granted) {
    return (
      <View style={styles.permissionContainer}>
        <Ionicons name="camera-off-outline" size={64} color={COLORS.ERROR} />
        <Text style={styles.permissionTitle}>Camera Access Denied</Text>
        <Text style={styles.permissionText}>
          Please enable camera permission in device settings to capture photos.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing={facing}
        ratio="16:9"
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={() => navigation.goBack()}>
            <Ionicons name="close" size={24} color={COLORS.SURFACE} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Capture Verification Photo</Text>
          <TouchableOpacity style={styles.headerButton} onPress={toggleCameraFacing}>
            <Ionicons name="camera-reverse-outline" size={24} color={COLORS.SURFACE} />
          </TouchableOpacity>
        </View>

        {/* Location Status */}
        <View style={styles.locationStatus}>
          {currentLocation ? (
            <View style={styles.locationInfo}>
              <Ionicons 
                name="location" 
                size={16} 
                color={currentLocation.accuracy <= LOCATION_ACCURACY_THRESHOLD ? COLORS.SUCCESS : COLORS.WARNING} 
              />
              <Text style={styles.locationText}>
                GPS: {currentLocation.accuracy <= LOCATION_ACCURACY_THRESHOLD ? 'Good' : 'Poor'} 
                ({Math.round(currentLocation.accuracy)}m)
              </Text>
            </View>
          ) : (
            <View style={styles.locationInfo}>
              <ActivityIndicator size="small" color={COLORS.SURFACE} />
              <Text style={styles.locationText}>Getting GPS location...</Text>
            </View>
          )}
          
          {locationError && (
            <TouchableOpacity style={styles.locationError} onPress={retryLocation}>
              <Ionicons name="warning" size={16} color={COLORS.ERROR} />
              <Text style={styles.locationErrorText}>{locationError}</Text>
              <Ionicons name="refresh" size={16} color={COLORS.ERROR} />
            </TouchableOpacity>
          )}
        </View>

        {/* Camera Controls */}
        <View style={styles.controls}>
          <View style={styles.controlsRow}>
            <View style={styles.controlSpacer} />
            
            <TouchableOpacity
              style={[
                styles.captureButton,
                (isCapturing || isCapturingPhoto || !currentLocation) && styles.captureButtonDisabled
              ]}
              onPress={capturePhoto}
              disabled={isCapturing || isCapturingPhoto || !currentLocation}
            >
              {isCapturing || isCapturingPhoto ? (
                <ActivityIndicator size="large" color={COLORS.SURFACE} />
              ) : (
                <View style={styles.captureButtonInner} />
              )}
            </TouchableOpacity>
            
            <View style={styles.controlSpacer} />
          </View>
          
          <Text style={styles.instructionText}>
            Ensure good lighting and GPS signal before capturing
          </Text>
        </View>
      </CameraView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
    padding: SPACING.XXL,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
    textAlign: 'center',
  },
  permissionText: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.LG,
  },
  permissionButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.LG,
    paddingVertical: SPACING.MD,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.MD,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  headerButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
  },
  headerTitle: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  locationStatus: {
    position: 'absolute',
    top: 120,
    left: SPACING.MD,
    right: SPACING.MD,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  locationText: {
    color: COLORS.SURFACE,
    fontSize: 12,
    marginLeft: SPACING.XS,
  },
  locationError: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 59, 48, 0.9)',
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 16,
    marginTop: SPACING.XS,
  },
  locationErrorText: {
    color: COLORS.SURFACE,
    fontSize: 12,
    marginHorizontal: SPACING.XS,
    flex: 1,
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingBottom: 40,
    paddingTop: SPACING.LG,
  },
  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.LG,
  },
  controlSpacer: {
    flex: 1,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.SURFACE,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  captureButtonDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.SURFACE,
  },
  instructionText: {
    color: COLORS.SURFACE,
    fontSize: 14,
    textAlign: 'center',
    marginTop: SPACING.MD,
    paddingHorizontal: SPACING.LG,
  },
});

export default CameraScreen;
