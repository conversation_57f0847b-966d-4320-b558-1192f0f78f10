// Verification Screen

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  TextInput,
  Switch,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import { initializeVerification, updateFormField, submitVerification } from '../../store/slices/verificationSlice';
import { VerificationStackParamList } from '../../navigation/MainTabNavigator';
import { VerificationForm } from '../../types';
import { COLORS, SPACING, BORDER_RADIUS, MIN_PHOTOS_REQUIRED } from '../../constants';

type NavigationProp = StackNavigationProp<VerificationStackParamList, 'Verification'>;
type RouteProp_ = RouteProp<VerificationStackParamList, 'Verification'>;

const VerificationScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp_>();
  const { caseId } = route.params;
  
  const dispatch = useDispatch<AppDispatch>();
  const { currentForm, photos, isSubmitting } = useSelector((state: RootState) => state.verification);
  const { selectedCase } = useSelector((state: RootState) => state.cases);

  useEffect(() => {
    dispatch(initializeVerification(caseId));
  }, [dispatch, caseId]);

  const handleFieldUpdate = (field: string, value: any) => {
    dispatch(updateFormField({ field, value }));
  };

  const handleCapturePhoto = () => {
    navigation.navigate('Camera', { caseId });
  };

  const handleSubmitVerification = () => {
    if (!currentForm) return;
    
    if (photos.length < MIN_PHOTOS_REQUIRED) {
      Alert.alert(
        'Insufficient Photos',
        `Please capture at least ${MIN_PHOTOS_REQUIRED} photos before submitting.`,
        [{ text: 'OK' }]
      );
      return;
    }
    
    if (!currentForm.findings.additionalNotes.trim()) {
      Alert.alert(
        'Missing Notes',
        'Please add verification notes before submitting.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    Alert.alert(
      'Submit Verification',
      'Are you sure you want to submit this verification? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Submit',
          onPress: async () => {
            try {
              const completedForm: VerificationForm = {
                ...currentForm,
                completedAt: new Date().toISOString(),
              };
              
              await dispatch(submitVerification({ form: completedForm, photos })).unwrap();
              
              Alert.alert(
                'Success',
                'Verification submitted successfully!',
                [{ text: 'OK', onPress: () => navigation.goBack() }]
              );
            } catch (error) {
              Alert.alert('Error', error as string);
            }
          },
        },
      ]
    );
  };

  if (!currentForm || !selectedCase) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading verification form...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Case Info Header */}
      <View style={styles.headerCard}>
        <Text style={styles.caseNumber}>{selectedCase.caseNumber}</Text>
        <Text style={styles.applicantName}>{selectedCase.applicantName}</Text>
        <Text style={styles.address}>{selectedCase.address}</Text>
      </View>

      {/* Photos Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Verification Photos</Text>
          <Text style={styles.photoCount}>
            {photos.length}/{MIN_PHOTOS_REQUIRED} required
          </Text>
        </View>
        
        <TouchableOpacity style={styles.captureButton} onPress={handleCapturePhoto}>
          <Ionicons name="camera" size={24} color={COLORS.PRIMARY} />
          <Text style={styles.captureButtonText}>Capture Photo</Text>
        </TouchableOpacity>
        
        {photos.length > 0 && (
          <View style={styles.photosList}>
            {photos.map((photo, index) => (
              <View key={photo.id} style={styles.photoItem}>
                <Ionicons name="image" size={20} color={COLORS.SUCCESS} />
                <Text style={styles.photoText}>Photo {index + 1}</Text>
                <Text style={styles.photoLocation}>
                  {photo.latitude.toFixed(4)}, {photo.longitude.toFixed(4)}
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>

      {/* Verification Form */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Verification Details</Text>
        
        <View style={styles.formRow}>
          <Text style={styles.formLabel}>Address is correct</Text>
          <Switch
            value={currentForm.findings.isAddressCorrect}
            onValueChange={(value) => handleFieldUpdate('findings.isAddressCorrect', value)}
            trackColor={{ false: COLORS.BORDER, true: COLORS.SUCCESS }}
            thumbColor={COLORS.SURFACE}
          />
        </View>
        
        <View style={styles.formRow}>
          <Text style={styles.formLabel}>Person/Contact available</Text>
          <Switch
            value={currentForm.findings.isPersonAvailable}
            onValueChange={(value) => handleFieldUpdate('findings.isPersonAvailable', value)}
            trackColor={{ false: COLORS.BORDER, true: COLORS.SUCCESS }}
            thumbColor={COLORS.SURFACE}
          />
        </View>
        
        {(selectedCase.verificationType === 'business' || selectedCase.verificationType === 'office') && (
          <View style={styles.formRow}>
            <Text style={styles.formLabel}>Business/Office operational</Text>
            <Switch
              value={currentForm.findings.isBusinessOperational || false}
              onValueChange={(value) => handleFieldUpdate('findings.isBusinessOperational', value)}
              trackColor={{ false: COLORS.BORDER, true: COLORS.SUCCESS }}
              thumbColor={COLORS.SURFACE}
            />
          </View>
        )}
        
        <View style={styles.formGroup}>
          <Text style={styles.formLabel}>Verification Result</Text>
          <View style={styles.radioGroup}>
            {['positive', 'negative', 'inconclusive'].map((result) => (
              <TouchableOpacity
                key={result}
                style={[
                  styles.radioOption,
                  currentForm.findings.verificationResult === result && styles.radioOptionSelected
                ]}
                onPress={() => handleFieldUpdate('findings.verificationResult', result)}
              >
                <View style={[
                  styles.radioCircle,
                  currentForm.findings.verificationResult === result && styles.radioCircleSelected
                ]} />
                <Text style={[
                  styles.radioText,
                  currentForm.findings.verificationResult === result && styles.radioTextSelected
                ]}>
                  {result.charAt(0).toUpperCase() + result.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.formLabel}>Additional Notes *</Text>
          <TextInput
            style={styles.textArea}
            placeholder="Enter detailed verification notes..."
            value={currentForm.findings.additionalNotes}
            onChangeText={(value) => handleFieldUpdate('findings.additionalNotes', value)}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
      </View>

      {/* Submit Button */}
      <View style={styles.submitContainer}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            (photos.length < MIN_PHOTOS_REQUIRED || isSubmitting) && styles.submitButtonDisabled
          ]}
          onPress={handleSubmitVerification}
          disabled={photos.length < MIN_PHOTOS_REQUIRED || isSubmitting}
        >
          <Text style={styles.submitButtonText}>
            {isSubmitting ? 'Submitting...' : 'Submit Verification'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  headerCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    padding: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  caseNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  applicantName: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  address: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  section: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  photoCount: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  captureButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.BACKGROUND,
    paddingVertical: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
    borderStyle: 'dashed',
  },
  captureButtonText: {
    color: COLORS.PRIMARY,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: SPACING.SM,
  },
  photosList: {
    marginTop: SPACING.MD,
  },
  photoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  photoText: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
    marginLeft: SPACING.SM,
    flex: 1,
  },
  photoLocation: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    fontFamily: 'monospace',
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  formGroup: {
    marginTop: SPACING.MD,
  },
  formLabel: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    paddingHorizontal: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    backgroundColor: COLORS.BACKGROUND,
  },
  radioOptionSelected: {
    backgroundColor: COLORS.PRIMARY,
  },
  radioCircle: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: COLORS.TEXT_SECONDARY,
    marginRight: SPACING.XS,
  },
  radioCircleSelected: {
    borderColor: COLORS.SURFACE,
    backgroundColor: COLORS.SURFACE,
  },
  radioText: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
  },
  radioTextSelected: {
    color: COLORS.SURFACE,
    fontWeight: 'bold',
  },
  textArea: {
    backgroundColor: COLORS.BACKGROUND,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: BORDER_RADIUS.MD,
    padding: SPACING.MD,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    minHeight: 100,
  },
  submitContainer: {
    padding: SPACING.MD,
  },
  submitButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.MD,
    borderRadius: BORDER_RADIUS.MD,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  submitButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default VerificationScreen;
