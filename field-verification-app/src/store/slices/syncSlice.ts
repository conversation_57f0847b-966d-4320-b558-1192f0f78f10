// Sync Slice

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import NetInfo from '@react-native-community/netinfo';
import { SyncStatus } from '../../types';
import { databaseService } from '../../services/databaseService';
import { apiClient } from '../../services/apiClient';
import { SYNC_RETRY_ATTEMPTS, SYNC_RETRY_DELAY } from '../../constants';

interface SyncState {
  status: SyncStatus;
  isSyncing: boolean;
  error: string | null;
}

const initialState: SyncState = {
  status: {
    isOnline: false,
    pendingUploads: 0,
    failedUploads: 0,
  },
  isSyncing: false,
  error: null,
};

// Async thunks
export const checkNetworkStatus = createAsyncThunk(
  'sync/checkNetworkStatus',
  async () => {
    const netInfo = await NetInfo.fetch();
    return {
      isOnline: netInfo.isConnected === true,
      isInternetReachable: netInfo.isInternetReachable === true,
    };
  }
);

export const getSyncQueueStatus = createAsyncThunk(
  'sync/getSyncQueueStatus',
  async (_, { rejectWithValue }) => {
    try {
      const queue = await databaseService.getSyncQueue();
      const pendingUploads = queue.length;
      const failedUploads = queue.filter(item => item.retryCount >= SYNC_RETRY_ATTEMPTS).length;
      
      return { pendingUploads, failedUploads };
    } catch (error) {
      return rejectWithValue('Failed to get sync queue status');
    }
  }
);

export const syncData = createAsyncThunk(
  'sync/syncData',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      // Check network connectivity first
      const networkStatus = await dispatch(checkNetworkStatus()).unwrap();
      if (!networkStatus.isOnline || !networkStatus.isInternetReachable) {
        return rejectWithValue('No internet connection');
      }
      
      const queue = await databaseService.getSyncQueue();
      let successCount = 0;
      let failureCount = 0;
      
      for (const item of queue) {
        try {
          let success = false;
          
          switch (item.type) {
            case 'case_status_update':
              const statusResponse = await apiClient.patch(
                `/cases/${item.data.caseId}/status`,
                { status: item.data.status }
              );
              success = statusResponse.success;
              break;
              
            case 'verification_submission':
              const verificationResponse = await apiClient.post('/verifications', item.data);
              success = verificationResponse.success;
              break;
              
            case 'photo_upload':
              const formData = new FormData();
              formData.append('photo', {
                uri: item.data.uri,
                type: 'image/jpeg',
                name: `${item.data.id}.jpg`,
              } as any);
              formData.append('caseId', item.data.caseId);
              formData.append('latitude', item.data.latitude.toString());
              formData.append('longitude', item.data.longitude.toString());
              formData.append('timestamp', item.data.timestamp);
              formData.append('description', item.data.description || '');
              
              const photoResponse = await apiClient.uploadFile('/photos/upload', formData);
              success = photoResponse.success;
              break;
              
            default:
              console.warn(`Unknown sync type: ${item.type}`);
              success = false;
          }
          
          if (success) {
            await databaseService.removeSyncItem(item.id);
            successCount++;
          } else {
            if (item.retryCount < SYNC_RETRY_ATTEMPTS) {
              await databaseService.incrementSyncRetry(item.id);
            }
            failureCount++;
          }
        } catch (error) {
          console.error(`Sync failed for item ${item.id}:`, error);
          if (item.retryCount < SYNC_RETRY_ATTEMPTS) {
            await databaseService.incrementSyncRetry(item.id);
          }
          failureCount++;
        }
        
        // Add delay between requests to avoid overwhelming the server
        if (queue.indexOf(item) < queue.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      return {
        successCount,
        failureCount,
        lastSyncTime: new Date().toISOString(),
      };
    } catch (error) {
      return rejectWithValue('Sync operation failed');
    }
  }
);

export const retryFailedSync = createAsyncThunk(
  'sync/retryFailedSync',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      const queue = await databaseService.getSyncQueue();
      const failedItems = queue.filter(item => item.retryCount >= SYNC_RETRY_ATTEMPTS);
      
      // Reset retry count for failed items
      for (const item of failedItems) {
        await databaseService.incrementSyncRetry(item.id); // This will reset the count
      }
      
      // Trigger sync again
      return await dispatch(syncData()).unwrap();
    } catch (error) {
      return rejectWithValue('Failed to retry sync');
    }
  }
);

export const clearSyncQueue = createAsyncThunk(
  'sync/clearSyncQueue',
  async (_, { rejectWithValue }) => {
    try {
      const queue = await databaseService.getSyncQueue();
      for (const item of queue) {
        await databaseService.removeSyncItem(item.id);
      }
      return true;
    } catch (error) {
      return rejectWithValue('Failed to clear sync queue');
    }
  }
);

const syncSlice = createSlice({
  name: 'sync',
  initialState,
  reducers: {
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.status.isOnline = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateSyncStatus: (state, action: PayloadAction<Partial<SyncStatus>>) => {
      state.status = { ...state.status, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Check network status
      .addCase(checkNetworkStatus.fulfilled, (state, action) => {
        state.status.isOnline = action.payload.isOnline && action.payload.isInternetReachable;
      })
      
      // Get sync queue status
      .addCase(getSyncQueueStatus.fulfilled, (state, action) => {
        state.status.pendingUploads = action.payload.pendingUploads;
        state.status.failedUploads = action.payload.failedUploads;
      })
      .addCase(getSyncQueueStatus.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // Sync data
      .addCase(syncData.pending, (state) => {
        state.isSyncing = true;
        state.error = null;
      })
      .addCase(syncData.fulfilled, (state, action) => {
        state.isSyncing = false;
        state.status.lastSyncTime = action.payload.lastSyncTime;
        state.error = null;
        // Update pending/failed counts will be handled by getSyncQueueStatus
      })
      .addCase(syncData.rejected, (state, action) => {
        state.isSyncing = false;
        state.error = action.payload as string;
      })
      
      // Retry failed sync
      .addCase(retryFailedSync.pending, (state) => {
        state.isSyncing = true;
        state.error = null;
      })
      .addCase(retryFailedSync.fulfilled, (state, action) => {
        state.isSyncing = false;
        state.status.lastSyncTime = action.payload.lastSyncTime;
        state.error = null;
      })
      .addCase(retryFailedSync.rejected, (state, action) => {
        state.isSyncing = false;
        state.error = action.payload as string;
      })
      
      // Clear sync queue
      .addCase(clearSyncQueue.fulfilled, (state) => {
        state.status.pendingUploads = 0;
        state.status.failedUploads = 0;
      })
      .addCase(clearSyncQueue.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { setOnlineStatus, clearError, updateSyncStatus } = syncSlice.actions;
export default syncSlice.reducer;
