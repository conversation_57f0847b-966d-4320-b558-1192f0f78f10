// Verification Slice

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { VerificationForm, VerificationPhoto } from '../../types';
import { databaseService } from '../../services/databaseService';
import { apiClient } from '../../services/apiClient';
import { CASE_STATUS } from '../../constants';

interface VerificationState {
  currentForm: VerificationForm | null;
  photos: VerificationPhoto[];
  isSubmitting: boolean;
  isCapturingPhoto: boolean;
  error: string | null;
}

const initialState: VerificationState = {
  currentForm: null,
  photos: [],
  isSubmitting: false,
  isCapturingPhoto: false,
  error: null,
};

// Async thunks
export const initializeVerification = createAsyncThunk(
  'verification/initialize',
  async (caseId: string, { rejectWithValue }) => {
    try {
      // Load existing photos for this case
      const photos = await databaseService.getPhotosByCase(caseId);
      return { caseId, photos };
    } catch (error) {
      return rejectWithValue('Failed to initialize verification');
    }
  }
);

export const savePhoto = createAsyncThunk(
  'verification/savePhoto',
  async (photo: VerificationPhoto, { rejectWithValue }) => {
    try {
      await databaseService.savePhoto(photo);
      return photo;
    } catch (error) {
      return rejectWithValue('Failed to save photo');
    }
  }
);

export const submitVerification = createAsyncThunk(
  'verification/submit',
  async (
    { form, photos }: { form: VerificationForm; photos: VerificationPhoto[] },
    { rejectWithValue }
  ) => {
    try {
      // Save verification form locally
      await databaseService.saveVerificationForm(form);
      
      // Update case status to completed
      await databaseService.updateCaseStatus(form.caseId, CASE_STATUS.COMPLETED);
      
      // Try to submit to API
      try {
        const response = await apiClient.post('/verifications', {
          ...form,
          photos: photos.map(photo => ({
            id: photo.id,
            uri: photo.uri,
            latitude: photo.latitude,
            longitude: photo.longitude,
            timestamp: photo.timestamp,
            description: photo.description,
          })),
        });
        
        if (response.success) {
          // Mark photos as synced
          for (const photo of photos) {
            photo.isSynced = true;
            await databaseService.savePhoto(photo);
          }
          
          // Update case status to synced
          await databaseService.updateCaseStatus(form.caseId, CASE_STATUS.SYNCED);
          
          return { form, synced: true };
        } else {
          // Add to sync queue
          await databaseService.addToSyncQueue('verification_submission', {
            form,
            photos,
          });
          return { form, synced: false };
        }
      } catch (apiError) {
        // Add to sync queue if network fails
        await databaseService.addToSyncQueue('verification_submission', {
          form,
          photos,
        });
        return { form, synced: false };
      }
    } catch (error) {
      return rejectWithValue('Failed to submit verification');
    }
  }
);

export const uploadPhotos = createAsyncThunk(
  'verification/uploadPhotos',
  async (photos: VerificationPhoto[], { rejectWithValue }) => {
    try {
      const uploadPromises = photos.map(async (photo) => {
        if (photo.isSynced) return photo;
        
        try {
          const formData = new FormData();
          formData.append('photo', {
            uri: photo.uri,
            type: 'image/jpeg',
            name: `${photo.id}.jpg`,
          } as any);
          formData.append('caseId', photo.caseId);
          formData.append('latitude', photo.latitude.toString());
          formData.append('longitude', photo.longitude.toString());
          formData.append('timestamp', photo.timestamp);
          formData.append('description', photo.description || '');
          
          const response = await apiClient.uploadFile('/photos/upload', formData);
          
          if (response.success) {
            photo.isSynced = true;
            await databaseService.savePhoto(photo);
            return photo;
          } else {
            throw new Error(response.error);
          }
        } catch (error) {
          // Keep photo in queue for retry
          return photo;
        }
      });
      
      const results = await Promise.all(uploadPromises);
      return results;
    } catch (error) {
      return rejectWithValue('Failed to upload photos');
    }
  }
);

const verificationSlice = createSlice({
  name: 'verification',
  initialState,
  reducers: {
    setCurrentForm: (state, action: PayloadAction<VerificationForm | null>) => {
      state.currentForm = action.payload;
    },
    updateFormField: (state, action: PayloadAction<{ field: string; value: any }>) => {
      if (state.currentForm) {
        const { field, value } = action.payload;
        if (field.startsWith('findings.')) {
          const findingField = field.replace('findings.', '');
          (state.currentForm.findings as any)[findingField] = value;
        } else {
          (state.currentForm as any)[field] = value;
        }
      }
    },
    addPhoto: (state, action: PayloadAction<VerificationPhoto>) => {
      state.photos.push(action.payload);
    },
    removePhoto: (state, action: PayloadAction<string>) => {
      state.photos = state.photos.filter(photo => photo.id !== action.payload);
    },
    updatePhotoDescription: (state, action: PayloadAction<{ photoId: string; description: string }>) => {
      const { photoId, description } = action.payload;
      const photo = state.photos.find(p => p.id === photoId);
      if (photo) {
        photo.description = description;
      }
    },
    clearVerification: (state) => {
      state.currentForm = null;
      state.photos = [];
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    setCapturingPhoto: (state, action: PayloadAction<boolean>) => {
      state.isCapturingPhoto = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Initialize verification
      .addCase(initializeVerification.pending, (state) => {
        state.error = null;
      })
      .addCase(initializeVerification.fulfilled, (state, action) => {
        const { caseId, photos } = action.payload;
        state.photos = photos;
        state.currentForm = {
          caseId,
          verificationType: 'residence', // Will be updated based on case
          photos: [],
          findings: {
            isAddressCorrect: false,
            isPersonAvailable: false,
            additionalNotes: '',
            verificationResult: 'inconclusive',
          },
          completedAt: '',
        };
        state.error = null;
      })
      .addCase(initializeVerification.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // Save photo
      .addCase(savePhoto.pending, (state) => {
        state.isCapturingPhoto = true;
      })
      .addCase(savePhoto.fulfilled, (state, action) => {
        state.isCapturingPhoto = false;
        state.photos.push(action.payload);
        state.error = null;
      })
      .addCase(savePhoto.rejected, (state, action) => {
        state.isCapturingPhoto = false;
        state.error = action.payload as string;
      })
      
      // Submit verification
      .addCase(submitVerification.pending, (state) => {
        state.isSubmitting = true;
        state.error = null;
      })
      .addCase(submitVerification.fulfilled, (state, action) => {
        state.isSubmitting = false;
        state.currentForm = null;
        state.photos = [];
        state.error = null;
      })
      .addCase(submitVerification.rejected, (state, action) => {
        state.isSubmitting = false;
        state.error = action.payload as string;
      })
      
      // Upload photos
      .addCase(uploadPhotos.fulfilled, (state, action) => {
        state.photos = action.payload;
      })
      .addCase(uploadPhotos.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  setCurrentForm,
  updateFormField,
  addPhoto,
  removePhoto,
  updatePhotoDescription,
  clearVerification,
  clearError,
  setCapturingPhoto,
} = verificationSlice.actions;

export default verificationSlice.reducer;
