// Cases Slice

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { VerificationCase } from '../../types';
import { CASE_STATUS } from '../../constants';
import { databaseService } from '../../services/databaseService';
import { apiClient } from '../../services/apiClient';

interface CasesState {
  cases: VerificationCase[];
  selectedCase: VerificationCase | null;
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  lastSyncTime: string | null;
}

const initialState: CasesState = {
  cases: [],
  selectedCase: null,
  isLoading: false,
  isRefreshing: false,
  error: null,
  lastSyncTime: null,
};

// Async thunks
export const fetchCases = createAsyncThunk(
  'cases/fetchCases',
  async (_, { rejectWithValue }) => {
    try {
      // Try to fetch from API first
      const response = await apiClient.get<VerificationCase[]>('/cases');
      
      if (response.success && response.data) {
        // Save to local database
        await databaseService.saveCases(response.data);
        return {
          cases: response.data,
          lastSyncTime: new Date().toISOString(),
        };
      } else {
        // Fallback to local database
        const localCases = await databaseService.getCases();
        return {
          cases: localCases,
          lastSyncTime: null,
        };
      }
    } catch (error) {
      // If API fails, load from local database
      try {
        const localCases = await databaseService.getCases();
        return {
          cases: localCases,
          lastSyncTime: null,
        };
      } catch (dbError) {
        return rejectWithValue('Failed to load cases');
      }
    }
  }
);

export const refreshCases = createAsyncThunk(
  'cases/refreshCases',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.get<VerificationCase[]>('/cases');
      
      if (response.success && response.data) {
        await databaseService.saveCases(response.data);
        return {
          cases: response.data,
          lastSyncTime: new Date().toISOString(),
        };
      } else {
        return rejectWithValue(response.error || 'Failed to refresh cases');
      }
    } catch (error) {
      return rejectWithValue('Network error. Please check your connection.');
    }
  }
);

export const updateCaseStatus = createAsyncThunk(
  'cases/updateCaseStatus',
  async (
    { caseId, status }: { caseId: string; status: string },
    { rejectWithValue }
  ) => {
    try {
      // Update local database immediately
      await databaseService.updateCaseStatus(caseId, status);
      
      // Try to sync with API
      try {
        const response = await apiClient.patch(`/cases/${caseId}/status`, { status });
        if (!response.success) {
          // Add to sync queue if API call fails
          await databaseService.addToSyncQueue('case_status_update', {
            caseId,
            status,
          });
        }
      } catch (apiError) {
        // Add to sync queue if network fails
        await databaseService.addToSyncQueue('case_status_update', {
          caseId,
          status,
        });
      }
      
      return { caseId, status };
    } catch (error) {
      return rejectWithValue('Failed to update case status');
    }
  }
);

export const acceptCase = createAsyncThunk(
  'cases/acceptCase',
  async (caseId: string, { dispatch, rejectWithValue }) => {
    try {
      await dispatch(updateCaseStatus({ caseId, status: CASE_STATUS.ACCEPTED })).unwrap();
      return caseId;
    } catch (error) {
      return rejectWithValue('Failed to accept case');
    }
  }
);

export const rejectCase = createAsyncThunk(
  'cases/rejectCase',
  async (caseId: string, { dispatch, rejectWithValue }) => {
    try {
      await dispatch(updateCaseStatus({ caseId, status: CASE_STATUS.REJECTED })).unwrap();
      return caseId;
    } catch (error) {
      return rejectWithValue('Failed to reject case');
    }
  }
);

export const startVerification = createAsyncThunk(
  'cases/startVerification',
  async (caseId: string, { dispatch, rejectWithValue }) => {
    try {
      await dispatch(updateCaseStatus({ caseId, status: CASE_STATUS.IN_PROGRESS })).unwrap();
      return caseId;
    } catch (error) {
      return rejectWithValue('Failed to start verification');
    }
  }
);

const casesSlice = createSlice({
  name: 'cases',
  initialState,
  reducers: {
    selectCase: (state, action: PayloadAction<string>) => {
      state.selectedCase = state.cases.find(case_ => case_.id === action.payload) || null;
    },
    clearSelectedCase: (state) => {
      state.selectedCase = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateCaseLocally: (state, action: PayloadAction<{ caseId: string; updates: Partial<VerificationCase> }>) => {
      const { caseId, updates } = action.payload;
      const caseIndex = state.cases.findIndex(case_ => case_.id === caseId);
      if (caseIndex !== -1) {
        state.cases[caseIndex] = { ...state.cases[caseIndex], ...updates };
      }
      if (state.selectedCase?.id === caseId) {
        state.selectedCase = { ...state.selectedCase, ...updates };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch cases
      .addCase(fetchCases.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCases.fulfilled, (state, action) => {
        state.isLoading = false;
        state.cases = action.payload.cases;
        state.lastSyncTime = action.payload.lastSyncTime;
        state.error = null;
      })
      .addCase(fetchCases.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Refresh cases
      .addCase(refreshCases.pending, (state) => {
        state.isRefreshing = true;
        state.error = null;
      })
      .addCase(refreshCases.fulfilled, (state, action) => {
        state.isRefreshing = false;
        state.cases = action.payload.cases;
        state.lastSyncTime = action.payload.lastSyncTime;
        state.error = null;
      })
      .addCase(refreshCases.rejected, (state, action) => {
        state.isRefreshing = false;
        state.error = action.payload as string;
      })
      
      // Update case status
      .addCase(updateCaseStatus.fulfilled, (state, action) => {
        const { caseId, status } = action.payload;
        const caseIndex = state.cases.findIndex(case_ => case_.id === caseId);
        if (caseIndex !== -1) {
          state.cases[caseIndex].status = status as any;
        }
        if (state.selectedCase?.id === caseId) {
          state.selectedCase.status = status as any;
        }
      })
      .addCase(updateCaseStatus.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { selectCase, clearSelectedCase, clearError, updateCaseLocally } = casesSlice.actions;
export default casesSlice.reducer;
