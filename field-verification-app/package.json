{"name": "field-verification-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "expo": "~53.0.16", "expo-camera": "^16.1.10", "expo-constants": "^17.1.6", "expo-file-system": "^18.1.11", "expo-image-picker": "^16.1.4", "expo-location": "^18.1.6", "expo-media-library": "^17.1.7", "expo-secure-store": "^14.2.3", "expo-sqlite": "^15.2.13", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.20.0", "typescript": "~5.8.3", "@types/react": "~19.0.10"}, "private": true}