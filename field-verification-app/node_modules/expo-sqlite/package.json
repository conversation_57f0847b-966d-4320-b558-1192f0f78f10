{"name": "expo-sqlite", "version": "15.2.13", "description": "Provides access to a database using SQLite (https://www.sqlite.org/). The database is persisted across restarts of your app.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "exports": {"./package.json": "./package.json", ".": {"default": "./build/index.js", "types": "./build/index.d.ts"}, "./kv-store": {"default": "./kv-store.js", "types": "./kv-store.d.ts"}, "./app.plugin.js": "./app.plugin.js"}, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "sqlite", "sql", "storage", "async-storage"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-sqlite"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/sqlite/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"await-lock": "^2.2.2"}, "devDependencies": {"@testing-library/react-native": "^13.1.0", "@types/better-sqlite3": "^7.6.6", "better-sqlite3": "^11.6.0", "expo-module-scripts": "^4.1.8", "react-error-boundary": "^4.0.11"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "gitHead": "9731a6191dcab84e9c3a24492bbe70c56d6f5cc3"}