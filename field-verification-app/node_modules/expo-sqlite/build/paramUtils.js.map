{"version": 3, "file": "paramUtils.js", "sourceRoot": "", "sources": ["../src/paramUtils.ts"], "names": [], "mappings": "AASA;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAC7B,GAAG,MAAa;IAEhB,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAE,MAAM,CAAC,CAAC,CAAsB,CAAC;IAC9E,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;QACvB,UAAU,GAAG,EAAE,CAAC;IAClB,CAAC;IACD,IACE,OAAO,UAAU,KAAK,QAAQ;QAC9B,UAAU,YAAY,WAAW;QACjC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,EAC9B,CAAC;QACD,UAAU,GAAG,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC;IACD,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACpD,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QAC9B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAkC,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACpF,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;YACnB,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED,MAAM,eAAe,GAA8B,EAAE,CAAC;IACtD,MAAM,UAAU,GAAyB,EAAE,CAAC;IAC5C,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,KAAK,YAAY,UAAU,EAAE,CAAC;YAChC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,OAAO,CAAC,eAAe,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;AAC1D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAI,WAA8B,EAAE,YAAgC;IAC5F,yHAAyH;IACzH,MAAM,GAAG,GAAuE,EAAE,CAAC;IACnF,IAAI,WAAW,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,EAAE,CAAC;QAC/C,MAAM,IAAI,KAAK,CACb,kDAAkD,WAAW,CAAC,MAAM,aAAa,YAAY,CAAC,MAAM,EAAE,CACvG,CAAC;IACJ,CAAC;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IACD,OAAO,GAAQ,CAAC;AAClB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CACzB,WAA8B,EAC9B,gBAAsC;IAEtC,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAClC,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,WAAW,CAAC,MAAM,KAAK,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;QACtD,yFAAyF;QACzF,MAAM,IAAI,KAAK,CACb,kDAAkD,WAAW,CAAC,MAAM,aAAa,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAC9G,CAAC;IACJ,CAAC;IACD,MAAM,OAAO,GAAQ,EAAE,CAAC;IACxB,KAAK,MAAM,YAAY,IAAI,gBAAgB,EAAE,CAAC;QAC5C,yHAAyH;QACzH,MAAM,GAAG,GAAuE,EAAE,CAAC;QACnF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,GAAQ,CAAC,CAAC;IACzB,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["import {\n  SQLiteBindBlobParams,\n  SQLiteBindParams,\n  SQLiteBindPrimitiveParams,\n  SQLiteBindValue,\n  type SQLiteColumnNames,\n  type SQLiteColumnValues,\n} from './NativeStatement';\n\n/**\n * Normalize the bind params to data structure that can be passed to native module.\n * The data structure is a tuple of [primitiveParams, blobParams, shouldPassAsArray].\n * @hidden\n */\nexport function normalizeParams(\n  ...params: any[]\n): [SQLiteBindPrimitiveParams, SQLiteBindBlobParams, boolean] {\n  let bindParams = params.length > 1 ? params : (params[0] as SQLiteBindParams);\n  if (bindParams == null) {\n    bindParams = [];\n  }\n  if (\n    typeof bindParams !== 'object' ||\n    bindParams instanceof ArrayBuffer ||\n    ArrayBuffer.isView(bindParams)\n  ) {\n    bindParams = [bindParams];\n  }\n  const shouldPassAsArray = Array.isArray(bindParams);\n  if (Array.isArray(bindParams)) {\n    bindParams = bindParams.reduce<Record<string, SQLiteBindValue>>((acc, value, index) => {\n      acc[index] = value;\n      return acc;\n    }, {});\n  }\n\n  const primitiveParams: SQLiteBindPrimitiveParams = {};\n  const blobParams: SQLiteBindBlobParams = {};\n  for (const key in bindParams) {\n    const value = bindParams[key];\n    if (value instanceof Uint8Array) {\n      blobParams[key] = value;\n    } else {\n      primitiveParams[key] = value;\n    }\n  }\n\n  return [primitiveParams, blobParams, shouldPassAsArray];\n}\n\n/**\n * Compose `columnNames` and `columnValues` to an row object.\n * @hidden\n */\nexport function composeRow<T>(columnNames: SQLiteColumnNames, columnValues: SQLiteColumnValues): T {\n  // TODO(cedric): make these types more generic and tighten the returned object type based on provided column names/values\n  const row: { [key in SQLiteColumnNames[number]]: SQLiteColumnValues[number] } = {};\n  if (columnNames.length !== columnValues.length) {\n    throw new Error(\n      `Column names and values count mismatch. Names: ${columnNames.length}, Values: ${columnValues.length}`\n    );\n  }\n  for (let i = 0; i < columnNames.length; i++) {\n    row[columnNames[i]] = columnValues[i];\n  }\n  return row as T;\n}\n\n/**\n * Compose `columnNames` and `columnValuesList` to an array of row objects.\n * @hidden\n */\nexport function composeRows<T>(\n  columnNames: SQLiteColumnNames,\n  columnValuesList: SQLiteColumnValues[]\n): T[] {\n  if (columnValuesList.length === 0) {\n    return [];\n  }\n  if (columnNames.length !== columnValuesList[0].length) {\n    // We only check the first row because SQLite returns the same column count for all rows.\n    throw new Error(\n      `Column names and values count mismatch. Names: ${columnNames.length}, Values: ${columnValuesList[0].length}`\n    );\n  }\n  const results: T[] = [];\n  for (const columnValues of columnValuesList) {\n    // TODO(cedric): make these types more generic and tighten the returned object type based on provided column names/values\n    const row: { [key in SQLiteColumnNames[number]]: SQLiteColumnValues[number] } = {};\n    for (let i = 0; i < columnNames.length; i++) {\n      row[columnNames[i]] = columnValues[i];\n    }\n    results.push(row as T);\n  }\n  return results;\n}\n"]}