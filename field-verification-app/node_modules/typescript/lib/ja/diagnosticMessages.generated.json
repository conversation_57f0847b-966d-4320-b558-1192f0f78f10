{"ALL_COMPILER_OPTIONS_6917": "すべてのコンパイラ オプション", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "'{0}' 修飾子とインポート宣言は同時に使用できません。", "A_0_parameter_must_be_the_first_parameter_2680": "'{0}' パラメーターは最初のパラメーターである必要があります。", "A_JSDoc_template_tag_may_not_follow_a_typedef_callback_or_overload_tag_8039": "JSDoc '@template' タグが '@typedef'、'@callback'、または '@overload' タグの後にない可能性があります", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "JSDoc '@typedef' コメントに複数の '@type' タグを含めることはできません。", "A_bigint_literal_cannot_be_used_as_a_property_name_1539": "'bigint' リテラルをプロパティ名として使用することはできません。", "A_bigint_literal_cannot_use_exponential_notation_1352": "bigint リテラルでは指数表記を使用できません。", "A_bigint_literal_must_be_an_integer_1353": "bigint リテラルは整数である必要があります。", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "実装シグネチャでバインド パターン パラメーターを省略可能にすることはできません。", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "'break' ステートメントは外側のイテレーションまたは switch ステートメント内でのみ使用できます。", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "'break' ステートメントは、外側のステートメントのラベルにのみ移動できます。", "A_character_class_must_not_contain_a_reserved_double_punctuator_Did_you_mean_to_escape_it_with_backs_1522": "予約された二重句読点を文字クラスに含めることはできません。バックスラッシュを使用してエスケープするつもりでしたか?", "A_character_class_range_must_not_be_bounded_by_another_character_class_1516": "文字クラスの範囲を別の文字クラスでバインドすることはできません。", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "クラスで実装できるのは、オプションの型引数を指定した識別子/完全修飾名のみです。", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "クラスで実装できるのは、オブジェクト型または静的な既知のメンバーを持つオブジェクト型の積集合のみです。", "A_class_cannot_extend_a_primitive_type_like_0_Classes_can_only_extend_constructable_values_2863": "クラスが '{0}' のようなプリミティブ型を拡張することはできません。クラスは、コンストラクト可能な値のみを拡張できます。", "A_class_cannot_implement_a_primitive_type_like_0_It_can_only_implement_other_named_object_types_2864": "クラスが '{0}' のようなプリミティブ型を実装することはできません。実装できるのは、その他の名前付きオブジェクト型のみです。", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "'default' の修飾子がないクラス宣言には名前が必要です。", "A_class_member_cannot_have_the_0_keyword_1248": "クラス メンバーに '{0}' キーワードを指定することはできません。", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "コンマ式は計算されたプロパティ名では使用できません。", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "計算されたプロパティ名は、型パラメーターをそれを含む型から参照することはできません。", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "クラス プロパティ宣言内の計算されたプロパティ名には、単純なリテラル型または 'unique symbol' 型が必要です。", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "メソッド オーバーロード内の計算されたプロパティ名は、型がリテラル型または 'unique symbol' 型の式を参照する必要があります。", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "型リテラル内の計算されたプロパティ名は、型がリテラル型または 'unique symbol' 型の式を参照する必要があります。", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "環境コンテキスト内の計算されたプロパティ名は、型がリテラル型または 'unique symbol' 型の式を参照する必要があります。", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "インターフェイス内の計算されたプロパティ名は、型がリテラル型または 'unique symbol' 型の式を参照する必要があります。", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "計算されたプロパティ名は 'string' 型、'number' 型、'symbol' 型、または 'any' 型のいずれかでなければなりません。", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "'const' アサーションは、列挙型メンバーへの参照、文字列、数値、ブール値、配列、オブジェクト リテラルにのみ適用できます。", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "const 列挙型メンバーは、文字列リテラルを使用してのみアクセスできます。", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "環境コンテキストの 'const' 初期化子は、文字列または数値リテラル、もしくはリテラル列挙型の参照である必要があります。", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "コンストラクターのクラスが 'null' を拡張する場合、そのコンストラクターに 'super' の呼び出しを含めることはできません。", "A_constructor_cannot_have_a_this_parameter_2681": "コンストラクターに 'this' パラメーターを指定することはできません。", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "'continue' ステートメントは外側のイテレーション内でのみ使用できます。", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "'continue' ステートメントは、外側のイテレーション ステートメントのラベルにのみ移動できます。", "A_declaration_file_cannot_be_imported_without_import_type_Did_you_mean_to_import_an_implementation_f_2846": "宣言ファイルを 'import type' なしでインポートすることはできません。代わりに実装ファイルト '{0}' をインポートするつもりでしたか?", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "'declare' 修飾子は、環境コンテキストでは使用できません。", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "デコレーターが装飾できるのは、オーバーロードではなく、メソッドの実装のみです。", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "'default' 句を 'switch' ステートメントで複数回使用することはできません。", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "既定のエクスポートは、ECMAScript スタイルのモジュールでのみ使用できます。", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "既定のエクスポートは、ファイルまたはモジュールの宣言のトップレベルにある必要があります。", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "限定代入アサーション '!' は、このコンテキストで許可されていません。", "A_destructuring_declaration_must_have_an_initializer_1182": "非構造化宣言には初期化子が必要です。", "A_dynamic_import_call_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_for_t_2712": "ES5 の動的インポート呼び出しには、'Promise' コンストラクターが必要です。'Promise' コンストラクターの宣言があることを確認するか、'--lib' オプションに 'ES2015' を組み込んでください。", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "動的インポート呼び出しは 'Promise' を返します。'Promise' の宣言があること、または '--lib' オプションに 'ES2015' を含めていることをご確認ください。", "A_file_cannot_have_a_reference_to_itself_1006": "ファイルにそれ自体への参照を含めることはできません。", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "'never' を返す関数には、到達可能なエンド ポイントがありません。", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "'new' キーワードで呼び出される関数に、'void' である 'this' 型を使用することはできません。", "A_function_whose_declared_type_is_neither_undefined_void_nor_any_must_return_a_value_2355": "宣言された型が 'undefined'、'void'、または 'any' でない関数は、値を返す必要があります。", "A_generator_cannot_have_a_void_type_annotation_2505": "ジェネレーターに 'void' 型の注釈を指定することはできません。", "A_get_accessor_cannot_have_parameters_1054": "'get' アクセサーにパラメーターを指定することはできません。", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "get アクセサーは、少なくともセッターと同程度にアクセス可能である必要があります", "A_get_accessor_must_return_a_value_2378": "'get' アクセサーは値を返す必要があります。", "A_label_is_not_allowed_here_1344": "A ラベルはここでは使用できません。", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "ラベル付きのタプル要素を optional として宣言するには、型の後ではなく名前の後とコロンの前に疑問符を付けます。", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "ラベル付きのタプル要素を rest として宣言するには、型の前ではなく名前の前に '...' を付けます。", "A_mapped_type_may_not_declare_properties_or_methods_7061": "マップされた型では、プロパティまたはメソッドを宣言しない場合があります。", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "列挙型宣言のメンバー初期化子は、他の列挙型で定義されたメンバーを含め、その後で宣言されたメンバーを参照できません。", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "mixin クラスには、型 'any[]' の単一の rest パラメーターを持つコンストラクターが必要です。", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "抽象コンストラクト シグネチャを含む型変数から拡張される mixin クラスも、'abstract' として宣言する必要があります。", "A_module_cannot_have_multiple_default_exports_2528": "モジュールに複数の既定のエクスポートを含めることはできません。", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "名前空間宣言は、それとマージするクラスや関数と異なるファイルに配置できません。", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "名前空間宣言は、それとマージするクラスや関数より前に配置できません。", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "名前空間宣言は、名前空間またはモジュールの最上位レベルでのみ許可されます。", "A_namespace_declaration_should_not_be_declared_using_the_module_keyword_Please_use_the_namespace_key_1540": "'namespace' 宣言を 'module' キーワードを使用して宣言することはできません。代わりに 'namespace' キーワードを使用してください。", "A_non_dry_build_would_build_project_0_6357": "非 -dry ビルドを実行した場合、プロジェクト '{0}' がビルドされます", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "非 -dry ビルドを実行した場合、次のファイルが削除されます: {0}", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "non-dry build では、プロジェクト '{0}' の出力のタイムスタンプが更新されます", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "パラメーター初期化子は、関数またはコンストラクターの実装でのみ指定できます。", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "パラメーター プロパティは、rest パラメーターを使用して宣言することはできません。", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "パラメーター プロパティは、コンストラクターの実装でのみ指定できます。", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "パラメーター プロパティは、バインド パターンを使用して宣言することはできません。", "A_promise_must_have_a_then_method_1059": "Promise には 'then' メソッドが必要です。", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "型が 'unique symbol' 型のクラスのプロパティは、'static' と 'readonly' の両方である必要があります。", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "型が 'unique symbol' 型のインターフェイスまたは型リテラルのプロパティは、'readonly' である必要があります。", "A_required_element_cannot_follow_an_optional_element_1257": "必須要素を省略可能な要素の後に指定することはできません。", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "必須パラメーターを省略可能なパラメーターの後に指定することはできません。", "A_rest_element_cannot_contain_a_binding_pattern_2501": "rest 要素にバインド パターンを含めることはできません。", "A_rest_element_cannot_follow_another_rest_element_1265": "rest 要素を別の rest 要素の後に指定することはできません。", "A_rest_element_cannot_have_a_property_name_2566": "rest 要素にプロパティ名を指定することはできません。", "A_rest_element_cannot_have_an_initializer_1186": "rest 要素に初期化子を指定することはできません。", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "rest 要素は非構造化パターンの最後に指定する必要があります。", "A_rest_element_type_must_be_an_array_type_2574": "rest 要素型は配列型である必要があります。", "A_rest_parameter_cannot_be_optional_1047": "rest パラメーターを省略可能にすることはできません。", "A_rest_parameter_cannot_have_an_initializer_1048": "rest パラメーターに初期化子を指定することはできません。", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "rest パラメーターはパラメーター リストの最後に指定する必要があります。", "A_rest_parameter_must_be_of_an_array_type_2370": "rest パラメーターは配列型でなければなりません。", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "rest パラメーターまたはバインド パターンに末尾のコンマがない可能性があります。", "A_return_statement_can_only_be_used_within_a_function_body_1108": "'return' ステートメントは、関数本体でのみ使用できます。", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "'return' ステートメントの使用が無効です。クラスの静的ブロック内では使用できません。", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "'baseUrl' の相対的な場所を検索するためにインポートを再マップする一連のエントリ。", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "'set' アクセサーに、戻り値の型の注釈を指定することはできません。", "A_set_accessor_cannot_have_an_optional_parameter_1051": "'set' アクセサーに、省略可能パラメーターを指定することはできません。", "A_set_accessor_cannot_have_rest_parameter_1053": "'set' アクセサーに rest パラメーターを指定することはできません。", "A_set_accessor_must_have_exactly_one_parameter_1049": "'set' アクセサーにはパラメーターを 1 つだけ指定しなければなりません。", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "'set' アクセサーのパラメーターに初期化子を含めることはできません。", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "spread 引数には、組の種類を指定するか、rest パラメーターに渡す必要があります。", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "'super' の呼び出しは、初期化されたプロパティ、パラメーターのプロパティ、private 識別子が派生クラスに含まれている場合は、コンストラクターのルートレベルのステートメントである必要があります。", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "'super' の呼び出しは、初期化されたプロパティ、パラメーターのプロパティ、private 識別子が派生クラスに含まれている場合は、'super' や 'this' を参照するコンストラクターの最初のステートメントである必要があります。", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "'this' ベース型のガードはパラメーター ベース型のガードとは互換性がありません。", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "'this' 型はクラスまたはインターフェイスの静的でないメンバーでのみ使用できます。", "A_top_level_export_modifier_cannot_be_used_on_value_declarations_in_a_CommonJS_module_when_verbatimM_1287": "'verbatimModuleSyntax' が有効である場合、CommonJS モジュール内の値宣言でトップレベルの 'export' 修飾子を使用することはできません。", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "'tsconfig.json' ファイルは既に '{0}' で定義されています。", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "タプル メンバーを optional と rest の両方に指定することはできません。", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "タプル型に負の値のインデックスを指定することはできません。", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "累乗式の左辺で型アサーション式を使用することはできません。式を括弧で囲むことを検討してください。", "A_type_literal_property_cannot_have_an_initializer_1247": "型リテラル プロパティに初期化子を使用することはできません。", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "型のみのインポートでは既定のインポートまたは名前付きバインドを指定できますが、両方を指定することはできません。", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "型の述語は rest パラメーターを参照できません。", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "型の述語は、バインド パターン内の要素 '{0}' を参照できません。", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "型の述語は、関数およびメソッドの戻り値の型の位置でのみ使用できます。", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "type 述語の型はそのパラメーターの型に割り当て可能である必要があります。", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "'isolatedModules' と 'emitDecoratorMetadata' が有効になっている場合は、装飾された署名で参照される型を 'import type' または名前空間インポートでインポートする必要があります。", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "型が 'unique symbol' 型の変数は、'const' である必要があります。", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "'yield' 式は、ジェネレーター本文でのみ使用できます。", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "クラス '{1}' の抽象メソッド '{0}' には super 式を介してアクセスできません。", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "抽象メソッドは抽象クラス内でのみ使用できます。", "Abstract_properties_can_only_appear_within_an_abstract_class_1253": "抽象プロパティは抽象クラス内でのみ使用できます。", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "コンストラクター内でクラス '{1}' の抽象プロパティ '{0}' にアクセスできません。", "Accessibility_modifier_already_seen_1028": "アクセシビリティ修飾子は既に存在します。", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "アクセサーは ECMAScript 5 以上をターゲットにする場合にのみ使用できます。", "Accessors_must_both_be_abstract_or_non_abstract_2676": "アクセサーはどちらも抽象または非抽象である必要があります。", "Add_0_to_unresolved_variable_90008": "'{0}' を未解決の変数に追加します", "Add_a_return_statement_95111": "return ステートメントを追加する", "Add_a_return_type_to_the_function_declaration_9031": "関数宣言に戻り値の型を追加してください。", "Add_a_return_type_to_the_function_expression_9030": "関数式に戻り値の型を追加してください。", "Add_a_return_type_to_the_get_accessor_declaration_9032": "get アクセサー宣言に戻り値の型を追加してください。", "Add_a_return_type_to_the_method_9034": "メソッドに戻り値の型を追加してください", "Add_a_type_annotation_to_the_parameter_0_9028": "パラメーター {0} に型注釈を追加してください。", "Add_a_type_annotation_to_the_property_0_9029": "プロパティ {0} に型注釈を追加してください。", "Add_a_type_annotation_to_the_variable_0_9027": "変数 {0} に型注釈を追加してください。", "Add_a_type_to_parameter_of_the_set_accessor_declaration_9033": "set アクセサー宣言のパラメーターに型を追加してください。", "Add_all_missing_async_modifiers_95041": "不足しているすべての 'async' 修飾子を追加します", "Add_all_missing_attributes_95168": "不足しているすべての属性を追加する", "Add_all_missing_call_parentheses_95068": "見つからない呼び出しのかっこをすべて追加します", "Add_all_missing_function_declarations_95157": "不足しているすべての関数宣言を追加します", "Add_all_missing_imports_95064": "不足しているすべてのインポートを追加する", "Add_all_missing_members_95022": "不足しているすべてのメンバーを追加します", "Add_all_missing_override_modifiers_95162": "不足しているすべての 'override' 修飾子を追加する", "Add_all_missing_parameters_95190": "不足しているすべてのプロパティを追加してください", "Add_all_missing_properties_95166": "不足しているすべてのプロパティを追加する", "Add_all_missing_return_statement_95114": "不足しているすべての return ステートメントを追加する", "Add_all_missing_super_calls_95039": "不足しているすべての super の呼び出しを追加します", "Add_all_missing_type_annotations_90067": "不足しているすべての型注釈を追加してください", "Add_all_optional_parameters_95193": "すべてのオプション パラメーターを追加してください", "Add_annotation_of_type_0_90062": "型 '{0}' の注釈を追加してください", "Add_async_modifier_to_containing_function_90029": "含まれている関数に async 修飾子を追加します", "Add_await_95083": "'await' を追加する", "Add_await_to_initializer_for_0_95084": "'{0}' の初期化子に 'await' を追加する", "Add_await_to_initializers_95089": "初期化子に 'await' を追加する", "Add_braces_to_arrow_function_95059": "アロー関数に中かっこを追加します", "Add_const_to_all_unresolved_variables_95082": "すべての未解決の変数に 'const' を追加する", "Add_const_to_unresolved_variable_95081": "未解決の変数に 'const' を追加する", "Add_definite_assignment_assertion_to_property_0_95020": "プロパティ '{0}' に限定代入アサーションを追加します", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "初期化されていないすべてのプロパティに限定代入アサーションを追加します", "Add_export_to_make_this_file_into_a_module_95097": "'export {}' を追加して、このファイルをモジュールにする", "Add_extends_constraint_2211": "'extends' 制約を追加します。", "Add_extends_constraint_to_all_type_parameters_2212": "すべての型パラメーターに 'extends' 制約を追加する", "Add_import_from_0_90057": "\"{0}\" からのインポートの追加", "Add_index_signature_for_property_0_90017": "プロパティ '{0}' のインデックス シグネチャを追加する", "Add_initializer_to_property_0_95019": "プロパティ '{0}' に初期化子を追加します", "Add_initializers_to_all_uninitialized_properties_95027": "初期化されていないすべてのプロパティに初期化子を追加します", "Add_missing_attributes_95167": "不足している属性の追加", "Add_missing_call_parentheses_95067": "見つからない呼び出しのかっこを追加します", "Add_missing_comma_for_object_member_completion_0_95187": "オブジェクト メンバー補完 '{0}' に不足しているコンマを追加してください。", "Add_missing_enum_member_0_95063": "不足している列挙型メンバー '{0}' を追加する", "Add_missing_function_declaration_0_95156": "不足している関数宣言 '{0}' を追加します", "Add_missing_new_operator_to_all_calls_95072": "不足している 'new' 演算子をすべての呼び出しに追加する", "Add_missing_new_operator_to_call_95071": "不足している 'new' 演算子を呼び出しに追加する", "Add_missing_parameter_to_0_95188": "'{0}' に不足しているパラメーターを追加してください", "Add_missing_parameters_to_0_95189": "'{0}' に不足しているパラメーターを追加してください", "Add_missing_properties_95165": "不足しているすべてのプロパティの追加", "Add_missing_super_call_90001": "欠落している 'super()' 呼び出しを追加する", "Add_missing_typeof_95052": "不足している 'typeof' を追加します", "Add_names_to_all_parameters_without_names_95073": "名前のないすべてのパラメーターに名前を追加する", "Add_optional_parameter_to_0_95191": "オプション パラメーターを '{0}' に追加してください", "Add_optional_parameters_to_0_95192": "省略可能なパラメーターを '{0}' に追加する", "Add_or_remove_braces_in_an_arrow_function_95058": "アロー関数内の中かっこを追加または削除します", "Add_override_modifier_95160": "'override' 修飾子を追加する", "Add_parameter_name_90034": "パラメーター名を追加する", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "メンバー名と一致するすべての未解決の変数に修飾子を追加します", "Add_resolution_mode_import_attribute_95196": "'resolution-mode' インポート属性を追加する", "Add_resolution_mode_import_attribute_to_all_type_only_imports_that_need_it_95197": "'resolution-mode' インポート属性を、必要とするすべての型のみのインポートに追加する", "Add_return_type_0_90063": "戻り値の型 '{0}' を追加してください", "Add_satisfies_and_a_type_assertion_to_this_expression_satisfies_T_as_T_to_make_the_type_explicit_9035": "型を明示的にするには、この式に satisfies と型アサーションを追加してください (satisfies T as T)。", "Add_satisfies_and_an_inline_type_assertion_with_0_90068": "'{0}' を使用して satisfies とインライン型のアサーションを追加してください", "Add_to_all_uncalled_decorators_95044": "呼び出されていないすべてのデコレーターに '()' を追加します", "Add_ts_ignore_to_all_error_messages_95042": "すべてのエラー メッセージに '@ts-ignore' を追加します", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "インデックスを使用してアクセスした場合は、'undefined' を型に追加します。", "Add_undefined_to_optional_property_type_95169": "省略可能なプロパティ型に 'undefined' を追加します", "Add_undefined_type_to_all_uninitialized_properties_95029": "初期化されていないすべてのプロパティに未定義の型を追加します", "Add_undefined_type_to_property_0_95018": "プロパティ '{0}' に '未定義' の型を追加します", "Add_unknown_conversion_for_non_overlapping_types_95069": "重複していない型に対して 'unknown' 変換を追加する", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "重複していない型のすべての変換に 'unknown' を追加する", "Add_void_to_Promise_resolved_without_a_value_95143": "値なしで解決された Promise に 'void' を追加します", "Add_void_to_all_Promises_resolved_without_a_value_95144": "値なしで解決されたすべての Promise に 'void' を追加します", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "tsconfig.json ファイルを追加すると、TypeScript ファイルと JavaScript ファイルの両方を含むプロジェクトを整理できます。詳細については、https://aka.ms/tsconfig をご覧ください。", "All_declarations_of_0_must_have_identical_constraints_2838": "'{0}' のすべての宣言には、同一の制約が必要です。", "All_declarations_of_0_must_have_identical_modifiers_2687": "'{0}' のすべての宣言には、同一の修飾子が必要です。", "All_declarations_of_0_must_have_identical_type_parameters_2428": "'{0}' のすべての宣言には、同一の型パラメーターがある必要があります。", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "抽象メソッドの宣言はすべて連続している必要があります。", "All_destructured_elements_are_unused_6198": "非構造化要素はいずれも使用されていません。", "All_imports_in_import_declaration_are_unused_6192": "インポート宣言内のインポートはすべて未使用です。", "All_type_parameters_are_unused_6205": "すべての型パラメーターが使用されていません。", "All_variables_are_unused_6199": "すべての変数は未使用です。", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "JavaScript ファイルをプログラムの一部として使用することを許可します。'checkJS' オプションを使用して、これらのファイルからエラーを取得してください。", "Allow_accessing_UMD_globals_from_modules_6602": "モジュールから UMD グローバルへのアクセスを許可します。", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "既定のエクスポートがないモジュールからの既定のインポートを許可します。これは、型チェックのみのため、コード生成には影響を与えません。", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "モジュールに既定のエクスポートがない場合は、'import x from y' を許可します。", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "tslib からヘルパー関数をファイルごとに含めるのではなく、プロジェクトごとに 1 回インポートすることを許可します。", "Allow_imports_to_include_TypeScript_file_extensions_Requires_moduleResolution_bundler_and_either_noE_6407": "TypeScript ファイル拡張子を含めるインポートを許可してください。'--moduleResolution bundler' と '--noEmit' または '--emitDeclarationOnly' のいずれかを設定する必要があります。", "Allow_javascript_files_to_be_compiled_6102": "javascript ファイルのコンパイルを許可します。", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "モジュールを解決するときに複数のフォルダーを 1 つのフォルダーとして処理することを許可します。", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "既に含まれているファイル名 '{0}' は、ファイル名 '{1}' と大文字と小文字の指定だけが異なります。", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "アンビエント モジュール宣言では、相対モジュール名を指定できません。", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "アンビエント モジュールを、他のモジュールまたは名前空間内の入れ子にすることはできません。", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "AMD モジュールに複数の名前を代入することはできません。", "An_abstract_accessor_cannot_have_an_implementation_1318": "抽象アクセサーに実装を含めることはできません。", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "アクセシビリティ修飾子を private 識別子と共に使用することはできません。", "An_accessor_cannot_have_type_parameters_1094": "アクセサーに型パラメーターを指定することはできません。", "An_accessor_property_cannot_be_declared_optional_1276": "'accessor' プロパティはオプションとして宣言できません。", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "アンビエント モジュール宣言は、ファイルの最上位にのみ使用できます。", "An_argument_for_0_was_not_provided_6210": "'{0}' の引数が指定されていません。", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "このバインド パターンに一致する引数が指定されていません。", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "算術オペランドは 'any' 型、'number' 型、’bigint' 型、列挙型のいずれかである必要があります。", "An_arrow_function_cannot_have_a_this_parameter_2730": "アロー関数に 'this' パラメーターを指定することはできません。", "An_async_function_or_method_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_2705": "ES5 の非同期の関数またはメソッドには、'Promise' コンストラクターが必要です。'Promise' コンストラクターの宣言があることを確認するか、'--lib' オプションに 'ES2015' を組み込んでください。", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "非同期関数またはメソッドは 'Promise' を返す必要があります。'Promise' の宣言があること、または '--lib' オプションに 'ES2015' を含めていることを確認してください。", "An_async_iterator_must_have_a_next_method_2519": "非同期反復子には 'next()' メソッドが必要です。", "An_element_access_expression_should_take_an_argument_1011": "要素アクセス式では、引数を取る必要があります。", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "private 識別子を使用して列挙型メンバーに名前を付けることはできません。", "An_enum_member_cannot_have_a_numeric_name_2452": "列挙型メンバーに数値名を含めることはできません。", "An_enum_member_name_must_be_followed_by_a_or_1357": "列挙型メンバー名の後には、','、'='、'}' のいずれかを指定する必要があります。", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "使用可能なすべてのコンパイラ オプションを示す、この情報の拡張バージョン", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "エクスポートの代入は、エクスポートされた他の要素を含むモジュールでは使用できません。", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "エクスポートの代入は、名前空間では使用できません。", "An_export_assignment_cannot_have_modifiers_1120": "エクスポートの代入に修飾子を指定することはできません。", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "エクスポートの割り当ては、ファイルまたはモジュールの宣言のトップレベルにある必要があります。", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "エクスポート宣言は、モジュールの最上位レベルでのみ使用できます。", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "エクスポート宣言は、名前空間またはモジュールの最上位レベルでのみ使用できます。", "An_export_declaration_cannot_have_modifiers_1193": "エクスポート宣言に修飾子を指定することはできません。", "An_export_declaration_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolve_1283": "'verbatimModuleSyntax' が有効である場合、 'export =' 宣言は実際の値を参照する必要がありますが、'{0}' は型のみの宣言に解決されます。", "An_export_declaration_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers__1282": "'verbatimModuleSyntax' が有効である場合、'export =' 宣言は値を参照する必要がありますが、'{0}' は型のみを参照しています。", "An_export_default_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolves_to_1285": "'verbatimModuleSyntax' が有効である場合、'export default' は実際の値を参照する必要がありますが、'{0}' は型のみの宣言に解決されます。", "An_export_default_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers_to_a_1284": "'verbatimModuleSyntax' が有効である場合、'export default' は値を参照する必要がありますが、'{0}' は型のみを参照しています。", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "'void' 型の式は、真実性をテストできません。", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "拡張された Unicode エスケープ値は 0x0 と 0x10FFFF の間 (両端を含む) でなければなりません。", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "識別子またはキーワードを数値リテラルのすぐ後に指定することはできません。", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "実装は環境コンテキストでは宣言できません。", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "インポート エイリアスは、'export type' を使用してエクスポートされた宣言を参照できません。", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "インポート エイリアスは、'import type' を使用してインポートされた宣言を参照できません。", "An_import_alias_cannot_resolve_to_a_type_or_type_only_declaration_when_verbatimModuleSyntax_is_enabl_1288": "'verbatimModuleSyntax' が有効である場合、インポート エイリアスを型または型のみの宣言に解決することはできません。", "An_import_alias_cannot_use_import_type_1392": "インポート エイリアスで 'import type' を使用することはできません", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "インポート宣言は、モジュールの最上位レベルでのみ使用できます。", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "インポート宣言は、名前空間またはモジュールの最上位レベルでのみ使用できます。", "An_import_declaration_cannot_have_modifiers_1191": "インポート宣言に修飾子を指定することはできません。", "An_import_path_can_only_end_with_a_0_extension_when_allowImportingTsExtensions_is_enabled_5097": "'allowImportingTsExtensions' が有効である場合、インポート パスの末尾には '{0}' 拡張子のみを指定できます。", "An_index_signature_cannot_have_a_rest_parameter_1017": "インデックス シグネチャに rest パラメーターを指定することはできません。", "An_index_signature_cannot_have_a_trailing_comma_1025": "インデックス シグネチャの末尾にコンマを指定することはできません。", "An_index_signature_must_have_a_type_annotation_1021": "インデックス シグネチャには型の注釈が必要です。", "An_index_signature_must_have_exactly_one_parameter_1096": "インデックス シグネチャには、パラメーターを 1 つだけ指定しなければなりません。", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "インデックス シグネチャのパラメーターに疑問符を指定することはできません。", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "インデックス シグネチャのパラメーターにアクセシビリティ修飾子を指定することはできません。", "An_index_signature_parameter_cannot_have_an_initializer_1020": "インデックス シグネチャのパラメーターに初期化子を指定することはできません。", "An_index_signature_parameter_must_have_a_type_annotation_1022": "インデックス シグネチャのパラメーターには型の注釈が必要です。", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "インデックス シグネチャ パラメーターの型をリテラル型またはジェネリック型にすることはできません。代わりに、マップされたオブジェクト型の使用を検討してください。", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "インデックス シグネチャ パラメーター型は、'string'、'number'、'symbol'、またはテンプレート リテラルの型である必要があります。", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "インスタンス化式の後にプロパティ アクセスを続けることはできません。", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "インターフェイスが拡張するのは、オプションの型引数が指定された識別子/完全修飾名のみです。", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "インターフェイスが拡張できるのは、オブジェクト型または静的な既知のメンバーを持つオブジェクト型の積集合のみです。", "An_interface_cannot_extend_a_primitive_type_like_0_It_can_only_extend_other_named_object_types_2840": "インターフェイスが '{0}' のようなプリミティブ型を拡張することはできません。拡張できるのは、その他の名前付きオブジェクト型のみです。", "An_interface_property_cannot_have_an_initializer_1246": "インターフェイス プロパティに初期化子を使用することはできません。", "An_iterator_must_have_a_next_method_2489": "反復子には 'next()' メソッドが必要です。", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "@jsx pragma を JSX フラグメントで使用する場合は、@jsxFrag pragma が必要です。", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "オブジェクト リテラルに同じ名前の複数の get/set アクセサーを指定することはできません。", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "オブジェクト リテラルに同じ名前の複数のプロパティを指定することはできません。", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "オブジェクト リテラルには、同じ名前のプロパティおよびアクセサーを指定することはできません。", "An_object_member_cannot_be_declared_optional_1162": "オブジェクト メンバーを省略可能として宣言することはできません。", "An_object_s_Symbol_hasInstance_method_must_return_a_boolean_value_for_it_to_be_used_on_the_right_han_2861": "オブジェクトの '[Symbol.hasInstance]' メソッドを 'instanceof' 式の右側で使用するには、このメソッドがブール値を返す必要があります。", "An_optional_chain_cannot_contain_private_identifiers_18030": "省略可能なチェーンには、pirvate 識別子を含めることはできません。", "An_optional_element_cannot_follow_a_rest_element_1266": "省略可能な要素を rest 要素の後に指定することはできません。", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "'this' の外部値がこのコンテナーによってシャドウされています。", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "オーバーロード シグネチャをジェネレーターとして宣言することはできません。", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "累乗式の左辺で '{0}' 演算子を含む単項式を使用することはできません。式を括弧で囲むことを検討してください。", "Annotate_everything_with_types_from_JSDoc_95043": "すべてに JSDoc の型で注釈を付けます", "Annotate_types_of_properties_expando_function_in_a_namespace_90071": "名前空間内のプロパティ expando 関数の型に注釈を付けてください", "Annotate_with_type_from_JSDoc_95009": "JSDoc の型で注釈を付けます", "Another_export_default_is_here_2753": "別のエクスポートの既定値がここにあります。", "Any_Unicode_property_that_would_possibly_match_more_than_a_single_character_is_only_available_when_t_1528": "複数の文字と一致する可能性のある Unicode プロパティは、Unicode Sets (v) フラグが設定されている場合にのみ使用できます。", "Anything_that_would_possibly_match_more_than_a_single_character_is_invalid_inside_a_negated_characte_1518": "複数の文字と一致する可能性のあるものはすべて、負数化された文字クラス内では無効です。", "Are_you_missing_a_semicolon_2734": "セミコロンを忘れていませんか?", "Argument_expression_expected_1135": "引数式が必要です。", "Argument_for_0_option_must_be_Colon_1_6046": "'{0}' オプションの引数は {1} である必要があります。", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "動的インポートの引数にスプレッド要素は指定できません。", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "型 '{0}' の引数を型 '{1}' のパラメーターに割り当てることはできません。", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "型 '{0}' の引数を、'exactOptionalPropertyTypes: true' が指定されている型 '{1}' のパラメーターに割り当てることはできません。ターゲットのプロパティの型に 'undefined' を追加することを検討してください。", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "rest パラメーター '{0}' の引数が指定されませんでした。", "Array_element_destructuring_pattern_expected_1181": "配列要素の非構造化パターンが必要です。", "Arrays_with_spread_elements_can_t_inferred_with_isolatedDeclarations_9018": "spread 要素を含む配列を --isolatedDeclarations と共に推論することはできません。", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "アサーションでは、呼び出し先のすべての名前が明示的な型の注釈で宣言されている必要があります。", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "アサーションでは、呼び出し先が識別子または修飾名である必要があります。", "Assigning_properties_to_functions_without_declaring_them_is_not_supported_with_isolatedDeclarations__9023": "宣言せずに関数にプロパティを割り当てることは、--isolatedDeclarations ではサポートされていません。この関数に割り当てられたプロパティに明示的な宣言を追加してください。", "Asterisk_Slash_expected_1010": "'*/' が必要です。", "At_least_one_accessor_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9009": "少なくとも 1 つのアクセサーに、--isolatedDeclarations を含む明示的な型の注釈が必要です。", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "グローバル スコープの拡張を直接入れ子にできるのは、外部モジュールまたは環境モジュールの宣言内のみです。", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "グローバル スコープの拡張は、環境コンテキストに既にある場合を除いて、'declare' 修飾子を使用する必要があります。", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "プロジェクト '{0}' で型指定の自動検出が有効になっています。キャッシュの場所 '{2}' を使用して、モジュール '{1}' に対して追加の解決パスを実行しています。", "BUILD_OPTIONS_6919": "ビルド オプション", "Backwards_Compatibility_6253": "下位互換性", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "基底クラスの式ではクラスの型パラメーターを参照することはできません。", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "基底コンストラクターの戻り値の型 '{0}' が、オブジェクト型または静的な既知のメンバーを持つオブジェクト型の積集合ではありません。", "Base_constructors_must_all_have_the_same_return_type_2510": "既定コンストラクターの戻り値の型は、すべて同じである必要があります。", "Base_directory_to_resolve_non_absolute_module_names_6083": "相対モジュール名を解決するためのベース ディレクトリ。", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "ターゲットが ES2020 未満の場合、bigint リテラルは使用できません。", "Binary_digit_expected_1177": "2 進の数字が必要です。", "Binding_element_0_implicitly_has_an_1_type_7031": "バインド要素 '{0}' には暗黙的に '{1}' 型が含まれます。", "Binding_elements_can_t_be_exported_directly_with_isolatedDeclarations_9019": "バインド要素を --isolatedDeclarations と共に直接エクスポートすることはできません。", "Block_scoped_variable_0_used_before_its_declaration_2448": "ブロック スコープの変数 '{0}' が、宣言の前に使用されています。", "Build_a_composite_project_in_the_working_directory_6925": "作業ディレクトリに複合プロジェクトを作成します。", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "最新の状態であると思われるものを含むすべてのプロジェクトをビルドします。", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "最新でない場合は、1 つ以上のプロジェクトとその依存関係をビルドします", "Build_option_0_requires_a_value_of_type_1_5073": "ビルド オプション '{0}' には型 {1} の値が必要です。", "Building_project_0_6358": "プロジェクト \"{0}\" をビルドしています...", "Built_in_iterators_are_instantiated_with_a_TReturn_type_of_undefined_instead_of_any_6720": "組み込みの反復子は、'any' の代わりに 'undefined' の 'TReturn' 型を使用してインスタンス化されます。", "COMMAND_LINE_FLAGS_6921": "コマンドライン フラグ", "COMMON_COMMANDS_6916": "一般的なコマンド", "COMMON_COMPILER_OPTIONS_6920": "一般的なコンパイラ オプション", "Call_decorator_expression_90028": "デコレーター式を呼び出す", "Call_signature_return_types_0_and_1_are_incompatible_2202": "呼び出しシグネチャの戻り値の型 '{0}' と '{1}' には互換性がありません。", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "戻り値の型の注釈がない呼び出しシグネチャの戻り値の型は、暗黙的に 'any' になります。", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "引数なしの呼び出しシグネチャに、互換性のない戻り値の型 '{0}' と '{1}' が含まれています。", "Can_only_convert_logical_AND_access_chains_95142": "論理 AND のアクセス チェーンのみを変換できます", "Can_only_convert_named_export_95164": "名前付きエクスポートのみを変換できます", "Can_only_convert_property_with_modifier_95137": "修飾子を伴うプロパティの変換のみ可能です", "Can_only_convert_string_concatenations_and_string_literals_95154": "文字列の連結と文字列リテラルのみを変換できます", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "'{0}.{1}' にアクセスできません。'{0}' は型で、名前空間ではありません。'{0}[\"{1}\"]' で '{0}' のプロパティ '{1}' の型を取得するつもりでしたか?", "Cannot_access_0_from_another_file_without_qualification_when_1_is_enabled_Use_2_instead_1281": "'{1}' が有効である場合、修飾しないで別のファイルから '{0}' にアクセスすることはできません。代わりに '{2}' を使用してください。", "Cannot_access_ambient_const_enums_when_0_is_enabled_2748": "'{0}' が有効である場合、アンビエント const 列挙型にアクセスすることはできません。", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "'{0}' コンストラクター型を '{1}' コンストラクター型に割り当てることができません。", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "抽象コンストラクター型を非抽象コンストラクター型に割り当てることはできません。", "Cannot_assign_to_0_because_it_is_a_class_2629": "クラスであるため、'{0}' に割り当てることはできません。", "Cannot_assign_to_0_because_it_is_a_constant_2588": "定数であるため、'{0}' に代入することはできません。", "Cannot_assign_to_0_because_it_is_a_function_2630": "関数であるため、'{0}' に割り当てることはできません。", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "名前空間であるため、'{0}' に割り当てることはできません。", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "読み取り専用プロパティであるため、'{0}' に代入することはできません。", "Cannot_assign_to_0_because_it_is_an_enum_2628": "列挙型であるため、'{0}' に割り当てることはできません。", "Cannot_assign_to_0_because_it_is_an_import_2632": "インポートであるため、'{0}' に割り当てることはできません。", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "変数ではないため、'{0}' に割り当てられません。", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "プライベート メソッド '{0}' に割り当てることはできません。プライベート メソッドは書き込み可能ではありません。", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "モジュール '{0}' は、モジュール以外のエンティティに解決するので拡張できません。", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "モジュール '{0}' は、モジュール以外のエンティティに解決するため、値のエクスポートで拡張できません。", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "'--module' フラグが 'amd' か 'system' でない限り、オプション '{0}' を使用してモジュールをコンパイルできません。", "Cannot_create_an_instance_of_an_abstract_class_2511": "抽象クラスのインスタンスは作成できません。", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "反復子の 'next' メソッドには型 '{1}' が必要なため、値に反復をデリゲートすることはできませんが、含まれるジェネレーターは常に '{0}' を送信します。", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "'{0}' をエクスポートできません。モジュールからエクスポートできるのはローカル宣言のみです。", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "クラス '{0}' を拡張できません。Class コンストラクターがプライベートに設定されています。", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "インターフェイス '{0}' を拡張できません。'implements' ですか?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "現在のディレクトリに tsconfig.json ファイルが見つかりません: {0}。", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "指定されたディレクトリに tsconfig.json ファイルが見つかりません: '{0}'。", "Cannot_find_global_type_0_2318": "グローバル型 '{0}' が見つかりません。", "Cannot_find_global_value_0_2468": "グローバル値 '{0}' が見つかりません。", "Cannot_find_lib_definition_for_0_2726": "'{0}' のライブラリ定義が見つかりません。", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "'{0}' のライブラリ定義が見つかりません。'{1}' ですか?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "モジュール '{0}' が見つかりません。'--resolveJsonModule' を使用して '.json' 拡張子を持つモジュールをインポートすることをご検討ください。", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_nodenext_or_to_add_aliases_t_2792": "モジュール '{0}' が見つかりません。'moduleResolution' オプションを 'nodenext' に設定するか、'paths' オプションにエイリアスを追加するつもりでしたか?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "モジュール '{0}' またはそれに対応する型宣言が見つかりません。", "Cannot_find_name_0_2304": "名前 '{0}' が見つかりません。", "Cannot_find_name_0_Did_you_mean_1_2552": "'{0}' という名前は見つかりません。'{1}' ですか?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "名前 '{0}' が見つかりません。インスタンス メンバー 'this.{0}' ですか?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "名前 '{0}' が見つかりません。静的メンバー '{1}.{0}' ですか?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "名前 '{0}' が見つかりません。これを非同期関数に書き込むということですか?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "名前 '{0}' が見つかりません。ターゲット ライブラリを変更する必要がありますか? 'lib' コンパイラ オプションを '{1}' 以降に変更してみてください。", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "名前 '{0}' が見つかりません。ターゲット ライブラリを変更しますか? 'lib' コンパイラ オプションが 'dom' を含むように変更してみてください。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2867": "名前 '{0}' が見つかりません。Bun の型定義をインストールする必要がありますか?'npm i --save-dev @types/bun' をお試しください。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2868": "名前 '{0}' が見つかりません。Bun の型定義をインストールする必要がありますか?'npm i --save-dev @types/bun' を試してから、tsconfig 内の型フィールドに 'bun' を追加してください。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "名前 '{0}' が見つかりません。テスト ランナーの型定義をインストールする必要がありますか? `npm i --save-dev @types/jest` または `npm i --save-dev @types/mocha` をお試しください。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "名前 '{0}' が見つかりません。テスト ランナーの型定義をインストールする必要がありますか? `npm i --save-dev @types/jest` または `npm i --save-dev @types/mocha` を試してから、tsconfig の型フィールドに 'jest' または 'mocha' を追加してください。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "名前 '{0}' が見つかりません。jQuery の型定義をインストールする必要がありますか? `npm i --save-dev @types/jquery` をお試しください。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "名前 '{0}' が見つかりません。jQuery の型定義をインストールする必要がありますか? `npm i --save-dev @types/jquery` を試してから、tsconfig の型フィールドに 'jquery' を追加してみてください。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "名前 '{0}' が見つかりません。ノードの型定義をインストールする必要がありますか? `npm i --save-dev @types/node` をお試しください。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "名前 '{0}' が見つかりません。ノードの型定義をインストールする必要がありますか? `npm i --save-dev @types/node` を試してから、tsconfig の型フィールドに 'node' を追加してみてください。", "Cannot_find_namespace_0_2503": "名前空間 '{0}' が見つかりません。", "Cannot_find_namespace_0_Did_you_mean_1_2833": "'{0}' という名前空間は見つかりません。'{1}' ですか?", "Cannot_find_parameter_0_1225": "パラメーター '{0}' が見つかりません。", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "入力ファイルの共通サブディレクトリ パスが見つかりません。", "Cannot_find_type_definition_file_for_0_2688": "'{0}' の型定義ファイルが見つかりません。", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "型宣言ファイルをインポートできません。'{1}' の代わりに '{0}' をインポートすることを検討してください。", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "ブロック スコープ宣言 '{1}' と同じスコープ内の外部スコープ変数 '{0}' を初期化できません。", "Cannot_invoke_an_object_which_is_possibly_null_2721": "'null' の可能性があるオブジェクトを呼び出すことはできません。", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "'null' または 'undefined' の可能性があるオブジェクトを呼び出すことはできません。", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "'undefined' の可能性があるオブジェクトを呼び出すことはできません。", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "反復子の 'next' メソッドは型 '{1}' を予期するため、値を反復処理できませんが、配列の非構造化は常に '{0}' を送信します。", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "反復子の 'next' メソッドは型 '{1}' を予期するため、値を反復処理できませんが、配列展開は常に '{0}' を送信します。", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "反復子の 'next' メソッドは型 '{1}' を予期するため、値を反復処理できませんが、for-of は常に '{0}' を送信します。", "Cannot_move_statements_to_the_selected_file_95183": "選択したファイルにステートメントを移動できません", "Cannot_move_to_file_selected_file_is_invalid_95179": "ファイルに移動できません。選択したファイルは無効です", "Cannot_read_file_0_5083": "ファイル '{0}' を読み取れません。", "Cannot_read_file_0_Colon_1_5012": "ファイル '{0}' を読み取れません: {1}。", "Cannot_redeclare_block_scoped_variable_0_2451": "ブロック スコープの変数 '{0}' を再宣言することはできません。", "Cannot_redeclare_exported_variable_0_2323": "エクスポートされた変数 '{0}' を再び宣言できません。", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "catch 句で識別子 '{0}' を再宣言することはできません。", "Cannot_start_a_function_call_in_a_type_annotation_1441": "型の注釈で関数呼び出しを開始することはできません。", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "'--jsx' フラグが指定されていないと、JSX を使用できません。", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_0_is_enabled_1269": "'{0}' が有効である場合、型または型のみの名前空間で 'export import' を使用することはできません。", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "'--module' が 'none' である場合、インポート、エクスポート、モジュール拡張は使用できません。", "Cannot_use_namespace_0_as_a_type_2709": "名前空間 '{0}' を型として使用することはできません。", "Cannot_use_namespace_0_as_a_value_2708": "名前空間 '{0}' を値として使用することはできません。", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "デコレートされたクラスの静的プロパティ初期化子で 'this' を使用できません。", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "ファイル '{0}' は、参照先のプロジェクト '{1}' によって生成された '.tsbuildinfo' ファイルを上書きするため、書き込めません", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "複数の入力ファイルで上書きされることになるため、ファイル '{0}' を書き込めません。", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "入力ファイルを上書きすることになるため、ファイル '{0}' を書き込めません。", "Catch_clause_variable_cannot_have_an_initializer_1197": "catch 句の変数に初期化子を指定することはできません。", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "Catch 句の変数型の注釈を指定する場合は、'any' または 'unknown' にする必要があります。", "Change_0_to_1_90014": "'{0}' を '{1}' に変更する", "Change_all_extended_interfaces_to_implements_95038": "拡張されたすべてのインターフェイスを 'implements' に変更します", "Change_all_jsdoc_style_types_to_TypeScript_95030": "jsdoc スタイルのすべての型を TypeScript に変更します", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "jsdoc スタイルのすべての型を TypeScript に変更します (さらに、'| undefined' を null 許容型に追加します)", "Change_extends_to_implements_90003": "'extends' を 'implements' に変更する", "Change_spelling_to_0_90022": "スペルを '{0}' に変更する", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "宣言されているものの、コンストラクターで設定されていないクラス プロパティを確認します。", "Check_side_effect_imports_6806": "副作用のインポートを確認してください。", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "'bind'、'call'、'apply' のメソッドの引数が元の関数と一致することを確認します。", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "'{0}' が '{1}' - '{2}' の最長一致のプレフィックスであるかを確認しています。", "Circular_definition_of_import_alias_0_2303": "インポート エイリアス '{0}' の循環定義です。", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "構成: {0} の解決中に循環が検出されました", "Circularity_originates_in_type_at_this_location_2751": "この位置の型で循環が発生しています。", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "クラス '{0}' で定義されたインスタンス メンバー アクセサー '{1}' が、拡張されたクラス '{2}' ではインスタンス メンバー関数として定義されています。", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "クラス '{0}' で定義されたインスタンス メンバー関数 '{1}' が、拡張されたクラス '{2}' ではインスタンス メンバー アクセサーとして定義されています。", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "クラス '{0}' で定義されたインスタンス メンバー プロパティ '{1}' が、拡張されたクラス '{2}' ではインスタンス メンバー関数として定義されています。", "Class_0_incorrectly_extends_base_class_1_2415": "クラス '{0}' は基底クラス '{1}' を正しく拡張していません。", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "クラス '{0}' はクラス '{1}' を正しく実装していません。'{1}' を拡張し、そのメンバーをサブクラスとして継承しますか?", "Class_0_incorrectly_implements_interface_1_2420": "クラス '{0}' はインターフェイス '{1}' を正しく実装していません。", "Class_0_used_before_its_declaration_2449": "クラス '{0}' は宣言の前に使用されました。", "Class_constructor_may_not_be_a_generator_1368": "クラス コンストラクターをジェネレーターにすることはできません。", "Class_constructor_may_not_be_an_accessor_1341": "クラス コンストラクターをアクセサーにすることはできません。", "Class_declaration_cannot_implement_overload_list_for_0_2813": "クラスの宣言では '{0}' のオーバーロード リストを実装できません。", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "クラスの宣言で複数の '@augments' または '@extends' タグを含めることはできません。", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "クラス デコレーターは、静的プライベート識別子と共に使用することはできません。試験段階のデコレーターを削除することをご検討ください。", "Class_field_0_defined_by_the_parent_class_is_not_accessible_in_the_child_class_via_super_2855": "親クラスによって定義されたクラス フィールド '{0}' は、super を介して子クラスでアクセスすることはできません。", "Class_name_cannot_be_0_2414": "クラス名を '{0}' にすることはできません。", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "モジュール {0} を使用して ES5 をターゲットとするときに、クラス名を 'オブジェクト' にすることはできません。", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "クラス側の静的な '{0}' が基底クラス側の静的な '{1}' を正しく拡張していません。", "Classes_can_only_extend_a_single_class_1174": "クラスで拡張できるクラスは 1 つのみです。", "Classes_may_not_have_a_field_named_constructor_18006": "クラスに 'constructor' という名前のフィールドを含めることはできません。", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "クラスに含まれるコードは JavaScript の厳格モードで評価されます。このモードでは、'{0}' の使用は許可されません。詳細については、「https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode」を参照してください。", "Command_line_Options_6171": "コマンド ライン オプション", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "構成ファイルか、'tsconfig.json' を含むフォルダーにパスが指定されたプロジェクトをコンパイルします。", "Compiler_Diagnostics_6251": "コンパイラの診断", "Compiler_option_0_cannot_be_given_an_empty_string_18051": "コンパイラ オプション '{0}' に空の文字列を指定することはできません。", "Compiler_option_0_expects_an_argument_6044": "コンパイラ オプション '{0}' には引数が必要です。", "Compiler_option_0_may_not_be_used_with_build_5094": "コンパイラオプション '--{0} ' は '--build ' と共に使用できない場合があります。", "Compiler_option_0_may_only_be_used_with_build_5093": "コンパイラ オプション '--{0} ' は '--build ' とのみ使用できる場合があります。", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "値 '{1}' のコンパイラ オプション '{0}' が不安定です。夜間 TypeScript を使用して、このエラーを無効にします。'npm install -D typescript@next' を使用して更新してみてください。", "Compiler_option_0_requires_a_value_of_type_1_5024": "コンパイラ オプション '{0}' には {1} の型の値が必要です。", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "private 識別子を下位レベルに生成するときに、コンパイラは名前 '{0}' を予約します。", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "指定されたパスにある TypeScript プロジェクトをコンパイルします。", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "現在のプロジェクト (作業ディレクトリ内の tsconfig.json) のコンパイル", "Compiles_the_current_project_with_additional_settings_6929": "追加の設定を使用して、現在のプロジェクトをコンパイルします。", "Completeness_6257": "完全", "Composite_projects_may_not_disable_declaration_emit_6304": "複合プロジェクトで宣言の生成を無効にすることはできません。", "Composite_projects_may_not_disable_incremental_compilation_6379": "複合プロジェクトではインクリメンタル コンパイルを無効にできません。", "Computed_from_the_list_of_input_files_6911": "入力ファイルのリストから計算されます。", "Computed_properties_must_be_number_or_string_literals_variables_or_dotted_expressions_with_isolatedD_9014": "計算されるプロパティは、数値または文字列リテラル、変数、または --isolatedDeclarations を含むドット付き式である必要があります。", "Computed_property_names_are_not_allowed_in_enums_1164": "計算されたプロパティ名は列挙型では使用できません。", "Computed_property_names_on_class_or_object_literals_cannot_be_inferred_with_isolatedDeclarations_9038": "クラスまたはオブジェクト リテラル上の計算されたプロパティ名を --isolatedDeclarations と共に推論することはできません。", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "文字列値のメンバーを持つ列挙型では、計算値は許可されません。", "Concatenate_and_emit_output_to_single_file_6001": "出力を連結して 1 つのファイルを生成します。", "Conditions_to_set_in_addition_to_the_resolver_specific_defaults_when_resolving_imports_6410": "インポートを解決するときに、リゾルバー固有の既定値に加えて設定する条件です。", "Conflicts_are_in_this_file_6201": "このファイル内に競合があります。", "Consider_adding_a_declare_modifier_to_this_class_6506": "このクラスに 'declare' 修飾子を追加することを検討してください。", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "コンストラクト シグネチャの戻り値の型 '{0}' と '{1}' には互換性がありません。", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "戻り値の型の注釈がないコンストラクト シグネチャの戻り値の型は、暗黙的に 'any' になります。", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "引数のないコンストラクト シグネチャには、互換性のない戻り値の型 '{0}' と '{1}' が含まれています。", "Constructor_implementation_is_missing_2390": "コンストラクターの実装がありません。", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "クラス '{0}' のコンストラクターはプライベートであり、クラス宣言内でのみアクセス可能です。", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "クラス '{0}' のコンストラクターは保護されており、クラス宣言内でのみアクセス可能です。", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "共用体型で使用する場合、コンストラクターの型の表記はかっこで囲む必要があります。", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "交差型で使用する場合、コンストラクターの型の表記はかっこで囲む必要があります。", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "派生クラスのコンストラクターには 'super' の呼び出しを含める必要があります。", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "包含するファイルが指定されていないため、ルート ディレクトリを決定できません。'node_modules' フォルダーのルックアップをスキップします。", "Containing_function_is_not_an_arrow_function_95128": "含まれている関数はアロー関数ではありません", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "モジュール形式の JS ファイルを検出するために使用するメソッドを制御します。", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "型 '{0}' から型 '{1}' への変換は、互いに十分に重複できないため間違っている可能性があります。意図的にそうする場合は、まず式を 'unknown' に変換してください。", "Convert_0_to_1_in_0_95003": "'{0}' を '{0} の {1}' に変換します", "Convert_0_to_mapped_object_type_95055": "'{0}' をマップされたオブジェクト型に変換する", "Convert_all_const_to_let_95102": "すべての 'const' を 'let' に変換する", "Convert_all_constructor_functions_to_classes_95045": "すべてのコンストラクター関数をクラスに変換します", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "すべての無効な文字を HTML エンティティ コードに変換する", "Convert_all_re_exported_types_to_type_only_exports_1365": "すべての再エクスポートされた型を、型のみのエクスポートに変換する", "Convert_all_require_to_import_95048": "'require' をすべて 'import' に変換", "Convert_all_to_async_functions_95066": "すべてを非同期関数に変換する", "Convert_all_to_bigint_numeric_literals_95092": "すべてを bigint 数値リテラルに変換する", "Convert_all_to_default_imports_95035": "すべてを既定のインポートに変換します", "Convert_all_type_literals_to_mapped_type_95021": "すべての型リテラルをマップされた型に変換します", "Convert_all_typedef_to_TypeScript_types_95177": "すべての typedef を TypeScript 型に変換してください。", "Convert_arrow_function_or_function_expression_95122": "アロー関数または関数式を変換する", "Convert_const_to_let_95093": "'const' を 'let' に変換する", "Convert_default_export_to_named_export_95061": "既定のエクスポートを名前付きエクスポートに変換する", "Convert_function_declaration_0_to_arrow_function_95106": "関数宣言 '{0}' をアロー関数に変換する", "Convert_function_expression_0_to_arrow_function_95105": "関数の式 '{0}' をアロー関数に変換する", "Convert_function_to_an_ES2015_class_95001": "関数を ES2015 クラスに変換します", "Convert_invalid_character_to_its_html_entity_code_95100": "無効な文字をその html エンティティ コードに変換する", "Convert_named_export_to_default_export_95062": "名前付きエクスポートを既定のエクスポートに変換する", "Convert_named_imports_to_default_import_95170": "名前付きインポートを既定のインポートに変換する", "Convert_named_imports_to_namespace_import_95057": "名前付きインポートを名前空間インポートに変換します", "Convert_namespace_import_to_named_imports_95056": "名前空間インポートを名前付きインポートに変換します", "Convert_overload_list_to_single_signature_95118": "オーバーロード リストを単一のシグネチャに変換する", "Convert_parameters_to_destructured_object_95075": "パラメーターを非構造化オブジェクトに変換する", "Convert_require_to_import_95047": "'require' を 'import' に変換", "Convert_to_ES_module_95017": "ES モジュールに変換する", "Convert_to_a_bigint_numeric_literal_95091": "bigint 数値リテラルに変換する", "Convert_to_anonymous_function_95123": "匿名関数に変換する", "Convert_to_arrow_function_95125": "アロー関数に変換する", "Convert_to_async_function_95065": "非同期関数に変換する", "Convert_to_default_import_95013": "既定のインポートに変換する", "Convert_to_named_function_95124": "名前付き関数に変換する", "Convert_to_optional_chain_expression_95139": "オプションのチェーン式に変換します", "Convert_to_template_string_95096": "テンプレート文字列に変換する", "Convert_to_type_only_export_1364": "型のみのエクスポートに変換する", "Convert_typedef_to_TypeScript_type_95176": "typedef を TypeScript 型に変換してください。", "Corrupted_locale_file_0_6051": "ロケール ファイル {0} は破損しています。", "Could_not_convert_to_anonymous_function_95153": "匿名関数に変換できませんでした", "Could_not_convert_to_arrow_function_95151": "アロー関数に変換できませんでした", "Could_not_convert_to_named_function_95152": "名前付き関数に変換できませんでした", "Could_not_determine_function_return_type_95150": "関数の戻り値の型を特定できませんでした", "Could_not_find_a_containing_arrow_function_95127": "含まれているアロー関数が見つかりませんでした", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "モジュール '{0}' の宣言ファイルが見つかりませんでした。'{1}' は暗黙的に 'any' 型になります。", "Could_not_find_convertible_access_expression_95140": "変換可能なアクセス式が見つかりませんでした", "Could_not_find_export_statement_95129": "export ステートメントが見つかりませんでした", "Could_not_find_import_clause_95131": "インポート句が見つかりませんでした", "Could_not_find_matching_access_expressions_95141": "一致するアクセス式が見つかりませんでした", "Could_not_find_name_0_Did_you_mean_1_2570": "名前 '{0}' が見つかりませんでした。'{1}' ですか?", "Could_not_find_namespace_import_or_named_imports_95132": "名前空間のインポートまたは名前付きインポートが見つかりませんでした", "Could_not_find_property_for_which_to_generate_accessor_95135": "アクセサーを生成するプロパティが見つかりませんでした", "Could_not_find_variable_to_inline_95185": "インライン化する変数が見つかりませんでした。", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "次の拡張子を持つパス '{0}' を解決できませんでした: {1}。", "Could_not_write_file_0_Colon_1_5033": "ファイル '{0}' を書き込めませんでした: '{1}'。", "Create_source_map_files_for_emitted_JavaScript_files_6694": "生成された JavaScript ファイルのソース マップ ファイルを作成します。", "Create_sourcemaps_for_d_ts_files_6614": "d.ts ファイルのソースマップを作成します。", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "作業ディレクトリの推奨設定を使用して tsconfig.json を作成します。", "DIRECTORY_6038": "ディレクトリ", "Decimal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_1537": "文字クラスでは、10 進数のエスケープ シーケンスと前方参照を使用することはできません。", "Decimals_with_leading_zeros_are_not_allowed_1489": "先頭が 0 の 10 進数を使用することはできません。", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "この宣言は別のファイル内の宣言を拡張します。この操作はシリアル化できません。", "Declaration_emit_for_this_file_requires_preserving_this_import_for_augmentations_This_is_not_support_9026": "このファイルの宣言を生成するには、拡張のためにこのインポートを保持する必要があります。これは --isolatedDeclarations ではサポートされていません。", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "このファイルの宣言の生成では、プライベート名 '{0}' を使用する必要があります。明示的な型の注釈では、宣言の生成のブロックを解除できます。", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "このファイルの宣言の生成では、モジュール '{1}' からのプライベート名 '{0}' を使用する必要があります。明示的な型の注釈では、宣言の生成のブロックを解除できます。", "Declaration_emit_for_this_parameter_requires_implicitly_adding_undefined_to_its_type_This_is_not_sup_9025": "このパラメーターの宣言を生成するには、その型に未定義の値を暗黙的に追加する必要があります。これは --isolatedDeclarations ではサポートされていません。", "Declaration_expected_1146": "宣言が必要です。", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "宣言名が組み込みのグローバル識別子 '{0}' と競合しています。", "Declaration_or_statement_expected_1128": "宣言またはステートメントが必要です。", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "宣言またはステートメントが必要です。この '=' はステートメントのブロックに続くため、非構造化割り当てを作成する場合は、割り当て全体をかっこで囲む必要があります。", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "明確な代入アサーションを使った宣言には、型の注釈も指定する必要があります。", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "初期化子を使った宣言に明確な代入アサーションを含めることはできません。", "Declare_a_private_field_named_0_90053": "'{0}' という名前のプライベート フィールドを宣言します。", "Declare_method_0_90023": "メソッド '{0}' を宣言する", "Declare_private_method_0_90038": "プライベート メソッド '{0}' を宣言する", "Declare_private_property_0_90035": "プライベート プロパティ '{0}' を宣言します", "Declare_property_0_90016": "プロパティ '{0}' を宣言する", "Declare_static_method_0_90024": "静的メソッド '{0}' を宣言する", "Declare_static_property_0_90027": "静的プロパティ '{0}' を宣言する", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "デコレーター関数の戻り値の型 '{0}' は、型 '{1}' に割り当てられません。", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "デコレーター関数の戻り値の型は '{0}' ですが、\"void\" または \"any\" である必要があります。", "Decorator_used_before_export_here_1486": "ここで 'export' の前にデコレーターが使用されています。", "Decorators_are_not_valid_here_1206": "デコレーターはここでは無効です。", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "デコレーターを同じ名前の複数の get/set アクセサーに適用することはできません。", "Decorators_may_not_appear_after_export_or_export_default_if_they_also_appear_before_export_8038": "デコレーターが 'export' の前に使用されている場合は、'export' または 'export default' の後にデコレーターを使用することはできません。", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "デコレーターは、プロパティ宣言の名前とすべてのキーワードの前に置く必要があります。", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "既定の catch 句の変数は '任意' ではなく '不明' です。", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "モジュールの既定エクスポートがプライベート名 '{0}' を持っているか、使用しています。", "Default_exports_can_t_be_inferred_with_isolatedDeclarations_9037": "既定のエクスポートを --isolatedDeclarations と共に推論することはできません。", "Default_library_1424": "既定のライブラリ", "Default_library_for_target_0_1425": "ターゲット '{0}' の既定のライブラリ", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "次の識別子の定義が、別のファイル内の定義と競合しています: {0}", "Delete_all_unused_declarations_95024": "未使用の宣言をすべて削除します", "Delete_all_unused_imports_95147": "未使用の import をすべて削除します", "Delete_all_unused_param_tags_95172": "未使用の '@param' タグをすべて削除します", "Delete_the_outputs_of_all_projects_6365": "すべてのプロジェクトの出力を削除します。", "Delete_unused_param_tag_0_95171": "未使用の '@param' タグ '{0}' を削除します", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[非推奨] 代わりに '--jsxFactory' を使います。'react' JSX 発行を対象とするときに、createElement に対して呼び出されたオブジェクトを指定します", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[非推奨] 代わりに '--outFile' を使います。出力を連結して 1 つのファイルを生成します", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[非推奨] 代わりに '--skipLibCheck' を使います。既定のライブラリ宣言ファイルの型チェックをスキップします。", "Deprecated_setting_Use_outFile_instead_6677": "非推奨の設定です。代わりに 'outFile' をお使いください。", "Did_you_forget_to_use_await_2773": "'await' を使用することを忘れていませんか?", "Did_you_mean_0_1369": "'{0}' を意図していましたか?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "'{0}' が型 'new (...args: any[]) => {1}' に制約されることを意図していましたか?", "Did_you_mean_to_call_this_expression_6212": "この式を呼び出すことを意図していましたか?", "Did_you_mean_to_mark_this_function_as_async_1356": "この関数を 'async' とマークすることを意図していましたか?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "':' を使用するつもりでしたか? 含まれるオブジェクト リテラルが非構造化パターンの一部である場合、'=' はプロパティ名の後にのみ使用することができます。", "Did_you_mean_to_use_new_with_this_expression_6213": "この式で 'new' を使用することを意図していましたか?", "Digit_expected_1124": "数値が必要です", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "ディレクトリ '{0}' は存在していません。ディレクトリ内のすべての参照をスキップしています。", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "ディレクトリ '{0}' には package.json のスコープが含まれません。インポートは解決されません。", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "生成された JavaScript ファイルでの 'use strict' ディレクティブの追加を無効にします。", "Disable_checking_for_this_file_90018": "このファイルのチェックを無効にする", "Disable_emitting_comments_6688": "コメントの生成を無効にします。", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "JSDoc コメントに '@internal' を含む宣言の生成を無効にします。", "Disable_emitting_files_from_a_compilation_6660": "コンパイルからのファイルの出力を無効にします。", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "型チェック エラーが報告された場合は、ファイルの生成を無効にします。", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "生成されたコード内で 'const 列挙型' 宣言の消去を無効にします。", "Disable_error_reporting_for_unreachable_code_6603": "到達できないコードのエラー報告を無効にします。", "Disable_error_reporting_for_unused_labels_6604": "未使用のラベルのエラー報告を無効にします。", "Disable_full_type_checking_only_critical_parse_and_emit_errors_will_be_reported_6805": "完全な型チェックを無効にしてください (重大な解析エラーと生成エラーのみが報告されます)。", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "コンパイルされた出力での '__extends' などのカスタム ヘルパー関数の生成を無効にします。", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "既定の lib.d.ts を含むすべてのライブラリ ファイルを含めることを無効にします。", "Disable_loading_referenced_projects_6235": "参照されているプロジェクトの読み込みを無効にします。", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "複合プロジェクトを参照するときに宣言ファイルではなくソース ファイルを優先することを無効にします。", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "オブジェクト リテラルの作成時に余分なプロパティ エラーの報告を無効にします。", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "symlink を realpath に解決できないようにします。これは、ノードの同じフラグに関連しています。", "Disable_size_limitations_on_JavaScript_projects_6162": "JavaScript プロジェクトのサイズ制限を無効にします。", "Disable_solution_searching_for_this_project_6224": "このプロジェクトのソリューション検索を無効にします。", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "関数型の汎用シグネチャに対する厳密なチェックを無効にします。", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "JavaScript プロジェクトの型の取得を無効にする", "Disable_truncating_types_in_error_messages_6663": "エラー メッセージ内の型の切り捨てを無効にします。", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "参照先のプロジェクトの宣言ファイルの代わりにソース ファイルを使用することを無効にします。", "Disable_wiping_the_console_in_watch_mode_6684": "ウォッチ モードでのコンソールのワイプを無効にします。", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "プロジェクト内のファイル名の参照による型取得の推論を無効にします。", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "'import'、'require'、'<reference>' を使用して TypeScript がプロジェクトに追加するファイルの数を増やすことを無効にします。", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "同じファイルへの大文字小文字の異なる参照を許可しない。", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "トリプルスラッシュの参照やインポートしたモジュールをコンパイルされたファイルのリストに追加しないでください。", "Do_not_allow_runtime_constructs_that_are_not_part_of_ECMAScript_6721": "ECMAScript の一部ではないランタイム コンストラクトを許可しません。", "Do_not_emit_comments_to_output_6009": "コメントを出力しないでください。", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "'@internal' の注釈を含むコードの宣言を生成しないでください。", "Do_not_emit_outputs_6010": "出力しないでください。", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "エラーが報告される場合は、出力しないでください。", "Do_not_emit_use_strict_directives_in_module_output_6112": "モジュール出力で 'use strict' ディレクティブを生成しません。", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "生成されたコード内で const 列挙型宣言を消去しないでください。", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "コンパイルされた出力で '__extends' などのカスタム ヘルパー関数を生成しないでください。", "Do_not_include_the_default_library_file_lib_d_ts_6158": "既定のライブラリ ファイル (lib.d.ts) を含めないでください。", "Do_not_report_errors_on_unreachable_code_6077": "到達できないコードに関するエラーを報告しない。", "Do_not_report_errors_on_unused_labels_6074": "未使用のラベルに関するエラーを報告しない。", "Do_not_resolve_the_real_path_of_symlinks_6013": "symlink の実際のパスを解決しません。", "Do_not_transform_or_elide_any_imports_or_exports_not_marked_as_type_only_ensuring_they_are_written_i_6804": "型のみとしてマークされていないインポートまたはエクスポートを変換または削除せずに、'module' 設定に基づいて出力ファイルの形式で書き込まれていることを確認してください。", "Do_not_truncate_error_messages_6165": "エラー メッセージを切り捨てないでください。", "Duplicate_function_implementation_2393": "関数の実装が重複しています。", "Duplicate_identifier_0_2300": "識別子 '{0}' が重複しています。", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "識別子 '{0}' が重複しています。コンパイラは、モジュールの最上位のスコープに名前 '{1}' を予約します。", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "識別子 '{0}' が重複しています。コンパイラは非同期関数を含むモジュールの最上位のスコープに名前 '{1}' を予約します。", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "識別子 '{0}' が重複しています。静的初期化子で 'super' 参照を出力するときに、コンパイラは名前 '{1}' を予約します。", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "識別子 '{0}' が重複しています。コンパイラは宣言 '{1}' を使用して非同期関数をサポートします。", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "識別子 '{0}' が重複しています。静的要素とインスタンス要素は、同じプライベート名を共有できません。", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "識別子 'arguments' が重複しています。コンパイラは 'arguments' を使用して rest パラメーターを初期化します。", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "識別子 '_newTarget' が重複しています。コンパイラは変数宣言 '_newTarget' を使用して、'new.target' メタプロパティの参照をキャプチャします。", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "識別子 '_this' が重複しています。コンパイラは変数宣言 '_this' を使用して '_this' の参照をキャプチャします。", "Duplicate_index_signature_for_type_0_2374": "型 '{0}' のインデックス シグネチャが重複しています。", "Duplicate_label_0_1114": "ラベル '{0}' が重複しています。", "Duplicate_property_0_2718": "プロパティ '{0}' が重複しています。", "Duplicate_regular_expression_flag_1500": "正規表現フラグが重複しています。", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "動的インポートの指定子の型は 'string' である必要がありますが、ここでは型 '{0}' が指定されています。", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "動的インポートは、'--module' フラグが 'es2020'、'es2022'、'esnext'、'commonjs'、'amd'、'system'、'umd'、'node16'、'node18'、または 'nodenext' に設定されている場合にのみサポートされます。", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_set_of_attributes_as_arguments_1450": "動的インポートでは、引数として、モジュール指定子とオプションの属性セットのみを受け取ることができます", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_node18_1324": "動的インポートは、'--module' オプションが 'esnext'、'node16'、'node18'、'nodenext'、または 'preserve' に設定されている場合にのみ、2 番目の引数をサポートします。", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_module_is_set_to_preserve_1293": "'module' が 'preserve' に設定されている場合、CommonJS モジュールでは ESM 構文を使用できません。", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_verbatimModuleSyntax_is_enabled_1286": "'verbatimModuleSyntax' が有効である場合、CommonJS モジュールで ESM 構文は許可されません。", "Each_declaration_of_0_1_differs_in_its_value_where_2_was_expected_but_3_was_given_4125": "'{0}.{1}' の各宣言の値が異なります。'{2}' が必要ですが、'{3}' が指定されました。", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "共用体型 '{0}' の各メンバーにはコンストラクト シグネチャがありますが、これらのシグネチャはいずれも相互に互換性がありません。", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "共用体型 '{0}' の各メンバーにはシグネチャがありますが、これらのシグネチャはいずれも相互に互換性がありません。", "Editor_Support_6249": "エディター サポート", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "型 '{0}' の式を使用して型 '{1}' にインデックスを付けることはできないため、要素は暗黙的に 'any' 型になります。", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "インデックス式が型 'number' ではないため、要素に 'any' 型が暗黙的に指定されます。", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "型 '{0}' にはインデックス シグネチャがないため、要素は暗黙的に 'any' 型になります。", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "型 '{0}' にはインデックス シグネチャがないため、要素は暗黙的に 'any' 型になります。'{1}' を呼び出すことを意図していましたか?", "Emit_6246": "生成", "Emit_ECMAScript_standard_compliant_class_fields_6712": "ECMAScript 標準準拠クラス フィールドを生成します。", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "出力ファイルの最初に UTF-8 バイト順マーク(BOM) を生成します。", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "個々のファイルを持つ代わりに、複数のソース マップを含む単一ファイルを生成します。", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "デバッグのために実行するコンパイラの v8 CPU プロファイルを生成します。", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "CommonJS モジュールのインポートをサポートしやすくするために追加の JavaScript を生成します。これにより、互換性のある型に対して 'allowSyntheticDefaultImports' を使用できるようになります。", "Emit_class_fields_with_Define_instead_of_Set_6222": "Set ではなく Define を使用して、クラスのフィールドを生成します。", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "ソース ファイル内の修飾された宣言に対してデザイン型メタデータを生成します。", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "イテレーションのために、準拠性が高いものの、冗長でパフォーマンスが低い JavaScript を生成します。", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "単一ファイル内でソースマップと共にソースを生成します。'--inlineSourceMap' または '--sourceMap' を設定する必要があります。", "Enable_all_strict_type_checking_options_6180": "厳密な型チェックのオプションをすべて有効にします。", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "TypeScript の出力で色と書式設定を有効にして、コンパイラ エラーを読みやすくします。", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "プロジェクト参照での TypeScript プロジェクトの使用を許可する制約を有効にします。", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "関数で明示的に返されないコードパスのエラー報告を有効にします。", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "暗黙的な 'any' 型を含む式と宣言に関するエラー報告を有効にします。", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "switch ステートメントに case のフォールスルーがある場合のエラー報告を有効にします。", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "型チェックされた JavaScript ファイルでのエラー報告を有効にします。", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "ローカル変数が読み取られていない場合にエラー報告を有効にします。", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "'this' に 'any' 型が指定されている場合は、エラー報告を有効にします。", "Enable_experimental_support_for_legacy_experimental_decorators_6630": "従来の実験的なデコレーターの実験的なサポートを有効にしてください。", "Enable_importing_files_with_any_extension_provided_a_declaration_file_is_present_6264": "宣言ファイルが存在する場合、拡張子を持つファイルのインポートを有効にしてください。", "Enable_importing_json_files_6689": ".json ファイルのインポートを有効にします。", "Enable_lib_replacement_6808": "lib 置換を有効にします。", "Enable_project_compilation_6302": "プロジェクトのコンパイルを有効にします", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "厳格な 'bind'、'call'、'apply' メソッドを関数で有効にします。", "Enable_strict_checking_of_function_types_6186": "関数の型の厳密なチェックを有効にします。", "Enable_strict_checking_of_property_initialization_in_classes_6187": "クラス内のプロパティの初期化の厳密なチェックを有効にします。", "Enable_strict_null_checks_6113": "厳格な null チェックを有効にします。", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "構成ファイルで 'experimentalDecorators' オプションを有効にする", "Enable_the_jsx_flag_in_your_configuration_file_95088": "構成ファイルで '--jsx' フラグを有効にする", "Enable_tracing_of_the_name_resolution_process_6085": "名前解決の処理のトレースを有効にします。", "Enable_verbose_logging_6713": "詳細ログを有効にします。", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "すべてのインポートの名前空間オブジェクトを作成して、CommonJS と ES モジュール間の生成の相互運用性を有効にします。'allowSyntheticDefaultImports' を暗黙のうちに表します。", "Enables_experimental_support_for_ES7_decorators_6065": "ES7 デコレーター用の実験的なサポートを有効にします。", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "デコレーター用の型メタデータを発行するための実験的なサポートを有効にします。", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "インデックス付きの型を使用して宣言されたキーに対してインデックス付きアクセサーの使用を強制します。", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "派生クラスのオーバーライドするメンバーが override 修飾子でマークされていることを確認します。", "Ensure_that_casing_is_correct_in_imports_6637": "インポートの大文字と小文字の指定が正しいことを確認します。", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "各ファイルが他のインポートに依存しないで安全にトランスパイルできることを確認します。", "Ensure_use_strict_is_always_emitted_6605": "'use strict' が常に生成されることを確認します。", "Entering_conditional_exports_6413": "条件付きエクスポートを入力しています。", "Entry_point_for_implicit_type_library_0_1420": "暗黙的なタイプ ライブラリ '{0}' のエントリ ポイント", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "packageId が '{1}' の暗黙的なタイプ ライブラリ '{0}' のエントリ ポイント", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "compilerOptions で指定されたタイプ ライブラリ '{0}' のエントリ ポイント", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "compilerOptions で指定された packageId が '{1}' のタイプ ライブラリ '{0}' のエントリ ポイント", "Enum_0_used_before_its_declaration_2450": "列挙型 '{0}' は宣言の前に使用されました。", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "列挙型の宣言は、名前空間または他の列挙型の宣言とのみマージできます。", "Enum_declarations_must_all_be_const_or_non_const_2473": "列挙型宣言は、すべてが定数、またはすべてが非定数でなければなりません。", "Enum_member_expected_1132": "列挙型メンバーが必要です。", "Enum_member_following_a_non_literal_numeric_member_must_have_an_initializer_when_isolatedModules_is__18056": "'isolatedModules' が有効である場合、非リテラル数値メンバーの後に続く列挙型メンバーには初期化子が必要です。", "Enum_member_initializers_must_be_computable_without_references_to_external_symbols_with_isolatedDecl_9020": "列挙型メンバー初期化子は、--isolatedDeclarations を含む外部シンボルへの参照なしで計算可能である必要があります。", "Enum_member_must_have_initializer_1061": "列挙型メンバーには初期化子が必要です。", "Enum_name_cannot_be_0_2431": "列挙型の名前を '{0}' にすることはできません。", "Errors_Files_6041": "エラーの発生したファイル", "Escape_sequence_0_is_not_allowed_1488": "エスケープ シーケンス '{0}' は許可されていません。", "Examples_Colon_0_6026": "例: {0}", "Excessive_complexity_comparing_types_0_and_1_2859": "型 '{0}' と '{1}' の比較が複雑すぎます。", "Excessive_stack_depth_comparing_types_0_and_1_2321": "型 '{0}' と '{1}' を比較するスタックが深すぎます。", "Exiting_conditional_exports_6416": "条件付きエクスポートを終了しています。", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "{0}-{1} 型の引数が必要です。'@extends' タグで指定してください。", "Expected_0_arguments_but_got_1_2554": "{0} 個の引数が必要ですが、{1} 個指定されました。", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "{0} 引数が必要ですが、{1} が指定されました。'Promise' の型引数に 'void' を含めましたか?", "Expected_0_type_arguments_but_got_1_2558": "{0} 個の型引数が必要ですが、{1} 個が指定されました。", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "{0} 型の引数が必要です。'@extends' タグで指定してください。", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "1 個の引数が必要ですが、0 個しかありませんでした。'new Promise()' では、引数なしで呼び出すことができる 'resolve' を生成するための JSDoc ヒントが必要です。", "Expected_a_Unicode_property_name_1523": "Unicode プロパティ名が必要です。", "Expected_a_Unicode_property_name_or_value_1527": "Unicode プロパティ名または値が必要です。", "Expected_a_Unicode_property_value_1525": "Unicode プロパティ値が必要です。", "Expected_a_capturing_group_name_1514": "キャプチャ グループ名が必要です。", "Expected_a_class_set_operand_1520": "クラス セット オペランドが必要でした。", "Expected_at_least_0_arguments_but_got_1_2555": "最低でも {0} 個の引数が必要ですが、{1} 個指定されました。", "Expected_corresponding_JSX_closing_tag_for_0_17002": "'{0}' の対応する JSX 終了タグが必要です。", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "JSX フラグメントの対応する終了タグが必要です。", "Expected_for_property_initializer_1442": "プロパティ初期化子には '=' を期待しています。", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "'package.json' の '{0}' フィールドの型は '{1}' であるべきですが、'{2}' を取得しました。", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "明示的に指定されたモジュール解決の種類 '{0}'。", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "'target' オプションが 'es2016' 以降に設定されている場合を除き、'bigint' 値に対して累乗を実行することはできません。", "Export_0_from_module_1_90059": "'{0}' をモジュール '{1}' からエクスポートする", "Export_all_referenced_locals_90060": "参照されているすべてのローカルをエクスポートする", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "ECMAScript モジュールを対象にする場合は、エクスポート代入を使用できません。代わりに 'export default' または別のモジュール書式の使用をご検討ください。", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "代入のエクスポートは、'--module' フラグが 'system' の場合にはサポートされません。", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "エクスポート宣言が、'{0}' のエクスポートされた宣言と競合しています。", "Export_declarations_are_not_permitted_in_a_namespace_1194": "エクスポート宣言は名前空間でサポートされません。", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "エクスポート指定子 '{0}' がパス '{1}' の package.json のスコープに存在しません。", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "エクスポートされた型のエイリアス '{0}' にプライベート名 '{1}' が付いているか、その名前を使用しています。", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "エクスポートされた型エイリアス '{0}' がモジュール {2} のプライベート名 '{1}' を持っているか、使用しています。", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "エクスポートされた変数 '{0}' が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "エクスポートされた変数 '{0}' がプライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Exported_variable_0_has_or_is_using_private_name_1_4025": "エクスポートされた変数 '{0}' がプライベート名 '{1}' を持っているか、使用しています。", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "エクスポートとエクスポートの代入はモジュールの拡張では許可されていません。", "Expression_expected_1109": "式が必要です。", "Expression_must_be_enclosed_in_parentheses_to_be_used_as_a_decorator_1497": "式をデコレーターとして使用するには、かっこで囲む必要があります。", "Expression_or_comma_expected_1137": "式またはコンマが必要です。", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "式では大きすぎて表すことができないタプル型を生成します。", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "式は、複雑すぎて表現できない共用体型を生成します。", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "式は、コンパイラが基底クラスの参照をキャプチャするために使用する '_super' に解決されます。", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "式は、コンパイラが 'new.target' メタプロパティの参照をキャプチャするために使用する変数宣言 '_newTarget' に解決されます。", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "式は、コンパイラが 'this' の参照をキャプチャするために使用する変数宣言 '_this' に解決されます。", "Expression_type_can_t_be_inferred_with_isolatedDeclarations_9013": "式の型を --isolatedDeclarations と共に推論することはできません。", "Extends_clause_can_t_contain_an_expression_with_isolatedDeclarations_9021": "extends 句に --isolatedDeclarations を含む式を含めることはできません。", "Extends_clause_for_inferred_type_0_has_or_is_using_private_name_1_4085": "推論された型 '{0}' の extends 句がプライベート名 '{1}' を持っているか、使用しています。", "Extract_base_class_to_variable_90064": "基底クラスを変数に抽出する", "Extract_binding_expressions_to_variable_90066": "バインド式を変数に抽出してください", "Extract_constant_95006": "定数の抽出", "Extract_default_export_to_variable_90065": "変数への既定のエクスポートを抽出する", "Extract_function_95005": "関数の抽出", "Extract_to_0_in_1_95004": "{1} 内の {0} に抽出する", "Extract_to_0_in_1_scope_95008": "{1} スコープ内の {0} に抽出する", "Extract_to_0_in_enclosing_scope_95007": "外側のスコープ内の {0} に抽出する", "Extract_to_interface_95090": "インターフェイスに抽出する", "Extract_to_type_alias_95078": "型のエイリアスに抽出する", "Extract_to_typedef_95079": "typedef に抽出する", "Extract_to_variable_and_replace_with_0_as_typeof_0_90069": "変数に抽出し、'{0} as typeof {0}' で置き換えます", "Extract_type_95077": "Extract 型", "FILE_6035": "ファイル", "FILE_OR_DIRECTORY_6040": "ファイルまたはディレクトリ", "Failed_to_find_peerDependency_0_6283": "peerDependency '{0}' が見つかりませんでした。", "Failed_to_resolve_under_condition_0_6415": "条件 '{0}' で解決できませんでした。", "Fallthrough_case_in_switch_7029": "switch に case のフォールスルーがあります。", "File_0_does_not_exist_6096": "ファイル '{0}' が存在しません。", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "以前にキャッシュされた検索によるとファイル '{0}' は存在しません。", "File_0_exists_according_to_earlier_cached_lookups_6239": "以前にキャッシュされた参照によるとファイル ' {0} ' は、存在します。", "File_0_exists_use_it_as_a_name_resolution_result_6097": "ファイル '{0}' が存在します。名前解決の結果として使用します。", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "ファイル '{0}' はサポートされていない拡張子を含んでいます。サポートされている拡張子は {1} のみです。", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "ファイル '{0}' は JavaScript ファイルです。'allowJs' オプションを有効にするつもりでしたか?", "File_0_is_not_a_module_2306": "ファイル '{0}' はモジュールではありません。", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "ファイル '{0}' がプロジェクト '{1}' のファイル リストに含まれていません。プロジェクトではすべてのファイルをリストするか、'include' パターンを使用する必要があります。", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "ファイル '{0}' が 'rootDir' '{1}' の下にありません。'rootDir' にすべてにソース ファイルが含まれている必要があります。", "File_0_not_found_6053": "ファイル '{0}' が見つかりません。", "File_Management_6245": "ファイルの管理", "File_appears_to_be_binary_1490": "ファイルはバイナリのようです。", "File_change_detected_Starting_incremental_compilation_6032": "ファイルの変更が検出されました。インクリメンタル コンパイルを開始しています...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "'{0}' にはフィールド \"type\" がないため、ファイルは CommonJS モジュールです", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "'{0}' にフィールド \"type\" があり、値が \"module\" ではないため、ファイルは CommonJS モジュールです。", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "\"package.json\" が見つからなかったため、ファイルは CommonJS モジュールです", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "'{0}' には値 \"module\" のフィールド \"type\" があるため、ファイルは ECMAScript モジュールです。", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "ファイルは CommonJS モジュールです。ES モジュールに変換される可能性があります。", "File_is_default_library_for_target_specified_here_1426": "ファイルはこちらで指定されたターゲットの既定のライブラリです。", "File_is_entry_point_of_type_library_specified_here_1419": "ファイルはこちらで指定されたタイプ ライブラリのエントリ ポイントです。", "File_is_included_via_import_here_1399": "ファイルはインポートによってこちらに追加されます。", "File_is_included_via_library_reference_here_1406": "ファイルはライブラリ参照によってこちらにインクルードされます。", "File_is_included_via_reference_here_1401": "ファイルは参照によってこちらにインクルードされます。", "File_is_included_via_type_library_reference_here_1404": "ファイルはタイプ ライブラリ参照によってこちらにインクルードされます。", "File_is_library_specified_here_1423": "ファイルはこちらで指定されたライブラリです。", "File_is_matched_by_files_list_specified_here_1410": "ファイルはこちらで指定された 'files' リストに一致します。", "File_is_matched_by_include_pattern_specified_here_1408": "ファイルはこちらで指定されたインクルード パターンに一致します。", "File_is_output_from_referenced_project_specified_here_1413": "ファイルはこちらで指定された参照先プロジェクトからの出力です。", "File_is_output_of_project_reference_source_0_1428": "ファイルはプロジェクト参照ソース '{0}' の出力です", "File_is_source_from_referenced_project_specified_here_1416": "ファイルはこちらで指定された参照先プロジェクトのソースです。", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "ファイル名 '{0}' は、既に含まれているファイル名 '{1}' と大文字と小文字の指定だけが異なります。", "File_name_0_has_a_1_extension_looking_up_2_instead_6262": "ファイル名 '{0}' の拡張子は '{1}' です。代わりに '{2}' を検索しています。", "File_name_0_has_a_1_extension_stripping_it_6132": "ファイル名 '{0}' に '{1}' 拡張子が使われています - 削除しています。", "File_redirects_to_file_0_1429": "ファイルはファイル '{0}' にリダイレクトされます", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "ファイルの指定で再帰ディレクトリのワイルドカード ('**') の後に親ディレクトリ ('..') を指定することはできません: '{0}'。", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "ファイルの指定の末尾を再帰的なディレクトリのワイルドカード ('**') にすることはできません: '{0}'。", "Filters_results_from_the_include_option_6627": "'include' オプションからの結果をフィルター処理します。", "Fix_all_detected_spelling_errors_95026": "検出されたすべてのスペル ミスを修正します", "Fix_all_expressions_possibly_missing_await_95085": "'await' が不足している可能性があるすべての式を修正する", "Fix_all_implicit_this_errors_95107": "すべての暗黙的な 'this' エラーを修正する", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "非同期関数の無効な戻り値の型をすべて修正します", "Fix_all_with_type_only_imports_95182": "型のみのインポートを使用してすべてを修正する", "Found_0_errors_6217": "{0} 件のエラーが見つかりました。", "Found_0_errors_Watching_for_file_changes_6194": "{0} 件のエラーが見つかりました。ファイルの変更をモニタリングしています。", "Found_0_errors_in_1_files_6261": "{1} ファイルに {0} 件のエラーが見つかりました。", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "同じファイル内に {0} 件のエラーが見つかりました。{1} から開始します", "Found_1_error_6216": "1 件のエラーが見つかりました。", "Found_1_error_Watching_for_file_changes_6193": "1 件のエラーが見つかりました。ファイルの変更をモニタリングしています。", "Found_1_error_in_0_6259": "{0} で 1 件のエラーが見つかりました", "Found_package_json_at_0_6099": "'{0}' で 'package.json' が見つかりました。", "Found_peerDependency_0_with_1_version_6282": "'{1}' バージョンの peerDependency '{0}' が見つかりました。", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_1250": "'ES5' を対象としている場合、関数宣言は厳格モードのブロック内では許可されていません。", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Class_definiti_1251": "'ES5' を対象としている場合、関数宣言は厳格モードのブロック内では許可されていません。クラス定義は自動的に厳格モードになります。", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Modules_are_au_1252": "'ES5' を対象としている場合、関数宣言は厳格モードのブロック内では許可されていません。モジュールは自動的に厳格モードになります。", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "戻り値の型の注釈がない関数式の戻り値の型は、暗黙的に '{0}' になります。", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "関数の実装がないか、宣言の直後に指定されていません。", "Function_implementation_name_must_be_0_2389": "関数の実装名は '{0}' でなければなりません。", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "関数は、戻り値の型の注釈がなく、いずれかの return 式で直接的にまたは間接的に参照されているため、戻り値の型は暗黙的に 'any' になります。", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "関数に終了の return ステートメントがないため、戻り値の型には 'undefined' が含まれません。", "Function_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9007": "関数には、--isolatedDeclarations を含む明示的な戻り値の型の注釈が必要です。", "Function_not_implemented_95159": "関数が実装されていません。", "Function_overload_must_be_static_2387": "関数のオーバーロードは静的でなければなりません。", "Function_overload_must_not_be_static_2388": "関数のオーバーロードは静的にはできせん。", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "共用体型で使用する場合、関数の型の表記はかっこで囲む必要があります。", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "交差型で使用する場合、関数の型の表記はかっこで囲む必要があります。", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "戻り値の型の注釈がない関数型の戻り値の型は、暗黙的に '{0}' になります。", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "本文を持つ関数は、アンビエントであるクラスとのみ結合できます。", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "プロジェクト内の TypeScript ファイルおよび JavaScript ファイルから、.d.ts ファイルを生成します。", "Generate_get_and_set_accessors_95046": "'get' および 'set' アクセサーの生成", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "すべてのオーバーライドするプロパティに対して 'get' および 'set' アクセサーを生成します", "Generates_a_CPU_profile_6223": "CPU プロファイルを生成します。", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "対応する各 '.d.ts' ファイルにソースマップを生成します。", "Generates_an_event_trace_and_a_list_of_types_6237": "イベント トレースと型のリストを生成します。", "Generates_corresponding_d_ts_file_6002": "対応する '.d.ts' ファイルを生成します。", "Generates_corresponding_map_file_6043": "対応する '.map' ファイルを生成します。", "Generator_implicitly_has_yield_type_0_Consider_supplying_a_return_type_annotation_7025": "ジェネレーターは暗黙的に yield 型 '{0}' を持っています。戻り値の型の注釈を指定することを検討してください。", "Generators_are_not_allowed_in_an_ambient_context_1221": "ジェネレーターは環境コンテキストでは使用できません。", "Generic_type_0_requires_1_type_argument_s_2314": "ジェネリック型 '{0}' には {1} 個の型引数が必要です。", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "ジェネリック型 '{0}' には、{1} 個から {2} 個までの型引数が必要です。", "Global_module_exports_may_only_appear_at_top_level_1316": "グローバル モジュールのエクスポートは最上位レベルにのみ出現可能です。", "Global_module_exports_may_only_appear_in_declaration_files_1315": "グローバル モジュールのエクスポートは宣言ファイルにのみ出現可能です。", "Global_module_exports_may_only_appear_in_module_files_1314": "グローバル モジュールのエクスポートはモジュール ファイルにのみ出現可能です。", "Global_type_0_must_be_a_class_or_interface_type_2316": "グローバル型 '{0}' はクラス型またはインターフェイス型でなければなりません。", "Global_type_0_must_have_1_type_parameter_s_2317": "グローバル型 '{0}' には {1} 個の型パラメーターが必要です。", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "'--incremental' と '--watch' での再コンパイルは、ファイル内の変更がそのファイルに直接依存しているファイルにのみ影響することを想定しています。", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "'incremental' と 'watch' モードを使用するプロジェクト内での再コンパイルは、ファイル内の変更がそれに直接依存しているファイルにのみ影響することを想定しています。", "Hexadecimal_digit_expected_1125": "16 進の数字が必要です。", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "識別子が必要です。'{0}' は、モジュールの最上位レベルでの予約語です。", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "識別子が必要です。'{0}' は厳格モードの予約語です。", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "識別子が必要です。'{0}' は厳格モードの予約語です。クラス定義は自動的に厳格モードになります。", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "識別子が必要です。'{0}' は、厳格モードの予約語です。モジュールは自動的に厳格モードになります。", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "識別子が予期されていました。'{0}' は、ここでは使用できない予約語です。", "Identifier_expected_1003": "識別子が必要です。", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "識別子が必要です。'__esModule' は、ECMAScript モジュールを変換するときのエクスポート済みマーカーとして予約されています。", "Identifier_or_string_literal_expected_1478": "識別子または文字列リテラルが必要です。", "Identifier_string_literal_or_number_literal_expected_1496": "識別子、文字列リテラル、または数値リテラルが必要です。", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "'{0}' パッケージが実際にこのモジュールを公開する場合は、pull request を送信して 'https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}' を修正することを検討してください", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "'{0}' パッケージが実際にこのモジュールを公開する場合は、'declare module '{1}';' を含む新しい宣言 (d.ts) ファイルを追加してみてください。", "Ignore_this_error_message_90019": "このエラー メッセージを無視する", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "tsconfig.json を無視し、既定のコンパイラ オプションを使用して指定されたファイルをコンパイルします。", "Implement_all_inherited_abstract_classes_95040": "継承されたすべての抽象クラスを実装します", "Implement_all_unimplemented_interfaces_95032": "実装されていないすべてのインターフェイスを実装します", "Implement_inherited_abstract_class_90007": "継承抽象クラスを実装する", "Implement_interface_0_90006": "インターフェイス '{0}' を実装する", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "エクスポートされたクラス '{0}' の Implements 句がプライベート名 '{1}' を持っているか、使用しています。", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "'symbol ' から 'string' への暗黙の変換は、実行時に失敗します。この式を 'String(...)' でラップすることを検討してください。", "Import_0_conflicts_with_global_value_used_in_this_file_so_must_be_declared_with_a_type_only_import_w_2866": "インポート '{0}' は、このファイルで使用されているグローバル値と競合するため、'isolatedModules' が有効な場合は、型のみのインポートで宣言する必要があります。", "Import_0_conflicts_with_local_value_so_must_be_declared_with_a_type_only_import_when_isolatedModules_2865": "インポート '{0}' は、グローバル値と競合するため、'isolatedModules' が有効な場合は、型のみのインポートで宣言する必要があります。", "Import_0_from_1_90013": "\"{1}\" から `{0}` をインポートします。", "Import_assertion_values_must_be_string_literal_expressions_2837": "インポート アサーションの値は、文字列リテラル式である必要があります。", "Import_assertions_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2836": "インポート アサーションは、commonjs 'require' 呼び出しにコンパイルするステートメントでは許可されません。", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2821": "インポート アサーションは、'--module' オプションが 'esnext'、'node18'、'nodenext' または 'preserve' に設定されている場合にのみサポートされます。", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "インポート アサーションは、型のみのインポートまたはエクスポートでは使用できません。", "Import_assertions_have_been_replaced_by_import_attributes_Use_with_instead_of_assert_2880": "インポート アサーションはインポート属性に置き換えられました。'assert' ではなく 'with' を使用してください。", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "ECMAScript モジュールを対象にする場合は、インポート代入を使用できません。代わりに 'import * as ns from \"mod\"'、'import {a} from \"mod\"'、'import d from \"mod\"' などのモジュール書式の使用をご検討ください。", "Import_attribute_values_must_be_string_literal_expressions_2858": "インポート 属性の値は、文字列リテラル式である必要があります。", "Import_attributes_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2856": "インポート属性は、commonjs 'require' 呼び出しにコンパイルするステートメントでは許可されません。", "Import_attributes_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2823": "インポート属性は、'--module' オプションが 'esnext'、'node18'、'nodenext' または 'preserve' に設定されている場合にのみサポートされます。", "Import_attributes_cannot_be_used_with_type_only_imports_or_exports_2857": "インポート属性は、型のみのインポートまたはエクスポートでは使用できません。", "Import_declaration_0_is_using_private_name_1_4000": "インポート宣言 '{0}' がプライベート名 '{1}' を使用しています。", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "インポート宣言が、'{0}' のローカル宣言と競合しています。", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "名前空間内のインポート宣言は、モジュールを参照できません。", "Import_emit_helpers_from_tslib_6139": "生成ヘルパーを 'tslib' からインポートします。", "Import_may_be_converted_to_a_default_import_80003": "インポートは既定のインポートに変換される可能性があります。", "Import_name_cannot_be_0_2438": "インポート名を '{0}' にすることはできません。", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "アンビエント モジュール宣言内のインポート宣言またはエクスポート宣言は、相対モジュール名を通してモジュールを参照することはできません。", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "インポート指定子 '{0}' がパス '{1}' の package.json のスコープに存在しません。", "Imported_via_0_from_file_1_1393": "ファイル '{1}' から {0} を介してインポートされました", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "compilerOptions で指定された 'importHelpers' をインポートするため、ファイル '{1}' から {0} を介してインポートされました", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "'jsx' および 'jsxs' ファクトリ関数をインポートするため、ファイル '{1}' から {0} を介してインポートされました", "Imported_via_0_from_file_1_with_packageId_2_1394": "packageId が '{2}' のファイル '{1}' から {0} を介してインポートされました", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "compilerOptions で指定されているように 'importHelpers' をインポートするため、packageId が '{2}' のファイル '{1}' から {0} を介してインポートされました", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "'jsx' および 'jsxs' ファクトリ関数をインポートするため、packageId が '{2}' のファイル '{1}' から {0} を介してインポートされました", "Importing_a_JSON_file_into_an_ECMAScript_module_requires_a_type_Colon_json_import_attribute_when_mod_1543": "ECMAScript モジュールに JSON ファイルをインポートするには、'module' が '{0}' に設定されている場合、'type: \"json\"' インポート属性が必要です。", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "インポートはモジュールの拡張では許可されていません。外側の外部モジュールに移動することを検討してください。", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "アンビエント列挙型の宣言では、メンバー初期化子は定数式である必要があります。", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "複数の宣言がある列挙型で、最初の列挙要素の初期化子を省略できる宣言は 1 つのみです。", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "ファイルの一覧を含めます。これは、'include' ではなく、glob パターンをサポートしていません。", "Include_modules_imported_with_json_extension_6197": "'.j<PERSON>' 拡張子付きのインポートされたモジュールを含める", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "生成された JavaScript 内のソースマップにソース コードを含めます。", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "出力された JavaScript 内にソースマップ ファイルを含めます。", "Includes_imports_of_types_referenced_by_0_90054": "'{0}' によって参照される型のインポートを含む", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "--watch を含めると、ファイルの変更について現在のプロジェクトの監視が開始されます。設定が完了すると、次の操作を使用してウォッチ モードを構成できます。", "Incomplete_quantifier_Digit_expected_1505": "不完全な量指定子です。数値が必要です。", "Index_signature_for_type_0_is_missing_in_type_1_2329": "型 '{0}' is missing in type '{1}' のインデックス シグネチャがありません。", "Index_signature_in_type_0_only_permits_reading_2542": "型 '{0}' のインデックス シグネチャは、読み取りのみを許可します。", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "マージされた宣言 '{0}' の個々の宣言はすべてエクスポートされるか、すべてローカルであるかのどちらかである必要があります。", "Infer_all_types_from_usage_95023": "使用法からすべての型を推論します", "Infer_function_return_type_95148": "関数の戻り値の型を推論します", "Infer_parameter_types_from_usage_95012": "使用状況からパラメーターの型を推論する", "Infer_this_type_of_0_from_usage_95080": "使い方から '{0}' の 'this' 型を推論する", "Infer_type_of_0_from_usage_95011": "使用状況から '{0}' の型を推論する", "Inference_from_class_expressions_is_not_supported_with_isolatedDeclarations_9022": "クラス式からの推論は、--isolatedDeclarations ではサポートされていません。", "Initialize_property_0_in_the_constructor_90020": "コンストラクターのプロパティ '{0}' を初期化する", "Initialize_static_property_0_90021": "静的プロパティ '{0}' を初期化する", "Initializer_for_property_0_2811": "プロパティ ' {0} ' の初期化子。", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "インスタンス メンバー変数 '{0}' の初期化子はコンストラクターで宣言された識別子 '{1}' を参照できません。", "Initializers_are_not_allowed_in_ambient_contexts_1039": "初期化子は環境コンテキストでは使用できません。", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "TypeScript プロジェクトを初期化して、tsconfig.json ファイルを作成します。", "Inline_variable_95184": "インライン変数", "Insert_command_line_options_and_files_from_a_file_6030": "コマンド ライン オプションとファイルをファイルから挿入します。", "Install_0_95014": "'{0}' のインストール", "Install_all_missing_types_packages_95033": "不足しているすべての型のパッケージをインストールします", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "インターフェイス '{0}' で型 '{1}' と型 '{2}' を同時には拡張できません。", "Interface_0_incorrectly_extends_interface_1_2430": "インターフェイス '{0}' はインターフェイス '{1}' を正しく拡張していません。", "Interface_declaration_cannot_have_implements_clause_1176": "インターフェイス宣言に 'implements' 句を指定することはできません。", "Interface_must_be_given_a_name_1438": "インターフェイスに名前を指定する必要があります。", "Interface_name_cannot_be_0_2427": "インターフェイス名を '{0}' にすることはできません。", "Interop_Constraints_6252": "制約の相互運用", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "'undefined' を追加するのではなく、省略可能なプロパティ型を記述済みとして解釈します。", "Invalid_character_1127": "無効な文字です。", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "無効なインポート指定子 '{0}' には解決策がありません。", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "拡張のモジュール名が無効です。モジュール '{0}' は '{1}' の型指定のないモジュールに解決されるため、拡張されません。", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "拡張のモジュール名が無効です。モジュール '{0}' が見つかりません。", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "新しい式の省略可能なチェーンが無効です。'{0}()' の呼び出しを意図していましたか?", "Invalid_reference_directive_syntax_1084": "無効な 'reference' ディレクティブ構文です。", "Invalid_syntax_in_decorator_1498": "デコレーターの構文が無効です。", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "'{0}' の使用が無効です。クラスの静的ブロック内では使用できません。", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "'{0}' の使用方法が無効です。モジュールは自動的に厳格モードになります。", "Invalid_use_of_0_in_strict_mode_1100": "厳格モードでは '{0}' の使用は無効です。", "Invalid_value_for_ignoreDeprecations_5103": "'--ignoreDeprecations' の値が無効です。", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "'jsxFactory' の値が無効です。'{0}' が有効な識別子または修飾名ではありません。", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "'jsxFragmentFactory' の値が無効です。'{0}' は有効な識別子でも修飾名でもありません。", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "'--reactNamespace' の値が無効です。'{0}' は有効な識別子ではありません。", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "これら 2 つのテンプレート式を区切るコンマが不足している可能性があります。タグ付きテンプレート式を形成しており、呼び出すことができません。", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "その要素の型 '{0}' は有効な JSX 要素ではありません。", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "そのインスタンスの型 '{0}' は、有効な JSX 要素ではありません。", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "その戻り値の型 '{0}' は、有効な JSX 要素ではありません。", "Its_type_0_is_not_a_valid_JSX_element_type_18053": "その型 '{0}' は有効な JSX 要素ではありません。", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "JSDoc '@{0} {1}' が 'extends {2}' 句と一致しません。", "JSDoc_0_is_not_attached_to_a_class_8022": "JSDoc '@{0}' はクラスにアタッチされていません。", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc '...' は、シグネチャの最後のパラメーターにのみ使用できます。", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "JSDoc '@param' タグの名前は '{0}' ですが、その名前のパラメーターはありません。", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "JSDoc '@param' タグに名前 '{0}' が指定されていますが、その名前のパラメーターはありません。配列型があった場合は、'arguments' と一致したはずです。", "JSDoc_typedef_may_be_converted_to_TypeScript_type_80009": "JSDoc typedef は TypeScript 型に変換できます。", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "JSDoc '@typedef' タグには、型の注釈を指定するか、後に '@property' タグや '@member' タグを付ける必要があります。", "JSDoc_typedefs_may_be_converted_to_TypeScript_types_80010": "JSDoc typedef は TypeScript 型に変換できます。", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "JSDoc の種類は、ドキュメント コメント内でのみ使用できます。", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "JSDoc の種類は TypeScript の種類に移行される可能性があります。", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "JSX 属性は、空ではない '式' にのみ割り当てる必要があります。", "JSX_element_0_has_no_corresponding_closing_tag_17008": "JSX 要素 '{0}' には対応する終了タグがありません。", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "JSX 要素クラスは '{0}' プロパティを含まないため、属性をサポートしません。", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "インターフェイス 'JSX.{0}' が存在しないため、暗黙的に JSX 要素の型は 'any' になります。", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "グローバル型 'JSX.Element' が存在しないため、JSX 要素は暗黙的に型 'any' になります。", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "JSX 要素型 '{0}' にはコンストラクトも呼び出しシグネチャも含まれていません。", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "JSX 要素に同じ名前の複数の属性を指定することはできません。", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "JSX 式では、コンマ演算子を使用できません。配列を作成するつもりでしたか?", "JSX_expressions_must_have_one_parent_element_2657": "JSX 式には 1 つの親要素が必要です。", "JSX_fragment_has_no_corresponding_closing_tag_17014": "JSX フラグメントには対応する終了タグがありません。", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "JSX プロパティ アクセス式に JSX 名前空間の名前を含めることはできません", "JSX_spread_child_must_be_an_array_type_2609": "JSX スプレッドの子は、配列型でなければなりません。", "JavaScript_Support_6247": "JavaScript サポート", "Jump_target_cannot_cross_function_boundary_1107": "ジャンプ先は関数の境界を越えることはできません。", "KIND_6034": "種類", "Keywords_cannot_contain_escape_characters_1260": "キーワードにエスケープ文字を含めることはできません。", "LOCATION_6037": "場所", "Language_and_Environment_6254": "言語と環境", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "コンマ演算子の左側が使用されていないため、副作用はありません。", "Library_0_specified_in_compilerOptions_1422": "compilerOptions でライブラリ '{0}' が指定されました", "Library_referenced_via_0_from_file_1_1405": "ファイル '{1}' から '{0}' を介してライブラリが参照されました", "Line_break_not_permitted_here_1142": "ここで改行することはできません。", "Line_terminator_not_permitted_before_arrow_1200": "行の終端記号をアローの前で使用することはできません。", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "モジュールを解決するときに検索するファイル名サフィックスのリスト。", "List_of_folders_to_include_type_definitions_from_6161": "含める型定義の元のフォルダーの一覧。", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "結合されたコンテンツがランタイムでのプロジェクトの構成を表すルート フォルダーの一覧。", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "ルート ディレクトリ '{1}' から '{0}' を読み込んでいます。候補の場所は '{2}' です。", "Loading_module_0_from_node_modules_folder_target_file_types_Colon_1_6098": "'node_modules' フォルダーからモジュール '{0}' を読み込んでいます。対象のファイルの種類は {1} です。", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_types_Colon_1_6095": "ファイル / フォルダーとしてモジュールを読み込んでいます。候補のモジュールの場所は '{0}'、対象のファイルの種類は {1} です。", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "ロケールは <language> または <language>-<territory> の形式で指定する必要があります (例: '{0}'、'{1}')。", "Log_paths_used_during_the_moduleResolution_process_6706": "'moduleResolution' の処理中に使用されたログ パス。", "Longest_matching_prefix_for_0_is_1_6108": "'{0}' の一致する最長プレフィックスは '{1}' です。", "Looking_up_in_node_modules_folder_initial_location_0_6125": "'node_modules' フォルダーを検索しています。最初の場所は '{0}' です。", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "すべての 'super()' 呼び出しをそのコンストラクターの最初のステートメントにします", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "keyof により、文字列、数字、記号の代わりに、文字列のみが返されるようにします。レガシ オプションです。", "Make_super_call_the_first_statement_in_the_constructor_90002": "'super()' 呼び出しをコンストラクター内の最初のステートメントにする", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "マップされたオブジェクト型のテンプレートの型は暗黙的に 'any' になります。", "Mark_array_literal_as_const_90070": "配列リテラルを const としてマークする", "Matched_0_condition_1_6403": "'{0}' 条件 '{1}' と一致しました。", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "既定で一致するインクルード パターン '**/*'", "Matched_by_include_pattern_0_in_1_1407": "'{1}' のインクルード パターン '{0}' に一致しています", "Member_0_implicitly_has_an_1_type_7008": "メンバー '{0}' の型は暗黙的に '{1}' になります。", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "メンバー '{0}' の型は暗黙的に '{1}' ですが、使い方からより良い型を推論する場合があります。", "Merge_conflict_marker_encountered_1185": "マージ競合マーカーが検出されました。", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "マージされた宣言 '{0}' に既定のエクスポート宣言を含めることはできません。代わりに、'export default {0}' 宣言を別個に追加することを検討してください。", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "メタプロパティ '{0}' は、関数の宣言の本文、関数の式、またはコンストラクターでのみ許可されています。", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "メソッド '{0}' は abstract に指定されているため、実装を含めることができません。", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "エクスポートされたインターフェイスのメソッド '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "エクスポートされたインターフェイスのメソッド '{0}' がプライベート名 '{1}' を持っているか、使用しています。", "Method_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9008": "メソッドには、--isolatedDeclarations を含む明示的な戻り値の型の注釈が必要です。", "Method_not_implemented_95158": "メソッドが実装されていません。", "Modifiers_cannot_appear_here_1184": "ここで修飾子を使用することはできません。", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "モジュール '{0}' は、'{1}' フラグを使用して既定でのみインポートできます", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "モジュール '{0}' はこのコンストラクトではインポートできません。指定子は ES モジュールに解決されるだけであるため、'require' でインポートすることはできません。代わりに ECMAScript インポートを使用してください。", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "モジュール '{0}' は '{1}' をローカルで宣言していますが、これは '{2}' としてエクスポートされています。", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "モジュール '{0}' は '{1}' をローカルで宣言していますが、これはエクスポートされていません。", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "モジュール '{0}' は型を参照していませんが、ここでは型として使用されています。'typeof import('{0}')' を意図していましたか?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "モジュール '{0}' は値を参照していませんが、ここでは値として使用されています。", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "モジュール {0} は既に '{1}' という名前のメンバーをエクスポートしています。あいまいさを解決するため、明示的にもう一度エクスポートすることを検討してください。", "Module_0_has_no_default_export_1192": "モジュール '{0}' に既定エクスポートがありません。", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "モジュール '{0}' には既定のエクスポートがありません。'import { {1} } from {0}' を使用するつもりでしたか?", "Module_0_has_no_exported_member_1_2305": "モジュール '{0}' にエクスポートされたメンバー '{1}' がありません。", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "モジュール '{0}' にはエクスポートされたメンバー '{1}' がありません。'import {1} from {0}' を使用するつもりでしたか?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "モジュール '{0}' は同じ名前のローカル宣言によって非表示になっています。", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "モジュール '{0}' には 'export =' が使用されているため、'export *' は併用できません。", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "モジュール '{0}' は、ファイル '{1}' のローカルで宣言されたアンビエント モジュールとして解決されました。", "Module_0_was_resolved_to_1_but_allowArbitraryExtensions_is_not_set_6263": "モジュール '{0}' は '{1}' に解決されましたが、'--allowArbitraryExtensions' が設定されていません。", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "モジュール '{0}' は '{1}' に解決されましたが、'--jsx' が設定されていません。", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "モジュール '{0}' は '{1}' に解決されましたが、'--resolveJsonModule' が使用されていません。", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "モジュール宣言名で使用できるのは、' または \"引用符で囲まれた文字列のみです。", "Module_name_0_matched_pattern_1_6092": "モジュール名 '{0}'、照合されたパターン '{1}'。", "Module_name_0_was_not_resolved_6090": "======== モジュール名 '{0}' が解決されませんでした。========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== モジュール名 '{0}' が正常に '{1}' に解決されました。========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== モジュール名 '{0}' が正常に '{1}' に解決されました (パッケージ ID '{2}')。========", "Module_resolution_kind_is_not_specified_using_0_6088": "モジュール解決の種類が '{0}' を使用して指定されていません。", "Module_resolution_using_rootDirs_has_failed_6111": "'rootDirs' を使用したモジュール解決が失敗しました。", "Modules_6244": "モジュール", "Move_labeled_tuple_element_modifiers_to_labels_95117": "ラベル付きのタプル要素の修飾子をラベルに移動する", "Move_the_expression_in_default_export_to_a_variable_and_add_a_type_annotation_to_it_9036": "既定のエクスポートで式を変数に移動し、それに型注釈を追加します。", "Move_to_a_new_file_95049": "新しいファイルへ移動します", "Move_to_file_95178": "ファイルに移動", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "複数の連続した数値区切り記号を指定することはできません。", "Multiple_constructor_implementations_are_not_allowed_2392": "コンストラクターを複数実装することはできません。", "NEWLINE_6061": "改行", "Name_is_not_valid_95136": "名前が無効です", "Named_capturing_groups_are_only_available_when_targeting_ES2018_or_later_1503": "名前付きキャプチャ グループは、'ES2018' 以降をターゲットにする場合にのみ使用できます。", "Named_capturing_groups_with_the_same_name_must_be_mutually_exclusive_to_each_other_1515": "同じ名前の名前の名前付きキャプチャ グループは、相互に排他的である必要があります。", "Named_imports_from_a_JSON_file_into_an_ECMAScript_module_are_not_allowed_when_module_is_set_to_0_1544": "'module' が '{0}' に設定されている場合、JSON ファイルから ECMAScript モジュールへの名前付きインポートは許可されません。", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "'{1}' 型および '{2}' 型の名前付きプロパティ '{0}' が一致しません。", "Namespace_0_has_no_exported_member_1_2694": "名前空間 '{0}' にエクスポートされたメンバー '{1}' がありません。", "Namespace_must_be_given_a_name_1437": "名前空間に名前を指定する必要があります。", "Namespace_name_cannot_be_0_2819": "名前空間名を '{0}' にすることはできません。", "Namespaces_are_not_allowed_in_global_script_files_when_0_is_enabled_If_this_file_is_not_intended_to__1280": "'{0}' が有効になっている場合、グローバル スクリプト ファイルでは名前空間を使用できません。このファイルがグローバル スクリプトを意図していない場合は、'moduleDetection' を 'force' に設定するか、空の 'export {}' ステートメントを追加してください。", "Neither_decorators_nor_modifiers_may_be_applied_to_this_parameters_1433": "デコレーターも修飾子も 'this' パラメーターに適用できません。", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "指定した数の型引数を持つ基底コンストラクターは存在しません。", "No_constituent_of_type_0_is_callable_2755": "型 '{0}' の構成要素は呼び出し可能ではありません。", "No_constituent_of_type_0_is_constructable_2759": "型 '{0}' の構成要素はコンストラクト可能ではありません。", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "型 '{0}' のパラメーターを持つインデックス シグネチャが型 '{1}' に見つかりませんでした。", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "構成ファイル '{0}' で入力が見つかりませんでした。指定された 'include' パスは '{1}' で、'exclude' パスは '{2}' でした。", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "サポートされていません。初期のバージョンの場合は、ファイルを読み取るためにテキストのエンコードを手動で設定してください。", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "{0} 引数を予期するオーバーロードはありませんが、{1} または {2} 引数のいずれかを予期するオーバーロードは存在します。", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "{0} 型の引数を予期するオーバーロードはありませんが、{1} または {2} 型の引数のいずれかを予期するオーバーロードは存在します。", "No_overload_matches_this_call_2769": "この呼び出しに一致するオーバーロードはありません。", "No_type_could_be_extracted_from_this_type_node_95134": "この型ノードからは型を抽出できませんでした", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "短縮形のプロパティ '{0}' のスコープには値がありません。値を宣言するか、または初期化子を指定してください。", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "非抽象クラス '{0}' は、クラス '{2}' からの継承抽象メンバー '{1}' を実装しません。", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_2654": "非抽象クラス '{0}' には、'{1}' の次のメンバーの実装がありません: {2}。", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_and_3_more_2655": "非抽象クラス '{0}' には、'{1}' の次のメンバーの実装がありません: {2} およびその他 {3}。", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "非抽象クラスの式はクラス '{1}' からの継承抽象メンバー '{0}' を実装しません。", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_2656": "非抽象クラス式に、'{0}' の次のメンバーの実装がありません: {1}。", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_and__2650": "非抽象クラス式に、'{0}' の次のメンバーの実装がありません: {1} およびその他 {2}。", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "non-null アサーションは、TypeScript ファイルでのみ使用できます。", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "'baseUrl' が設定されていない場合、非相対パスは許可されません。先頭に './' が使用されていることをご確認ください。", "Non_simple_parameter_declared_here_1348": "ここでは複雑なパラメーターが宣言されています。", "Not_all_code_paths_return_a_value_7030": "一部のコード パスは値を返しません。", "Not_all_constituents_of_type_0_are_callable_2756": "型 '{0}' のすべての構成要素が呼び出し可能なわけではありません。", "Not_all_constituents_of_type_0_are_constructable_2760": "型 '{0}' のすべての構成要素がコンストラクト可能なわけではありません。", "Numbers_out_of_order_in_quantifier_1506": "量指定子の数値が順不同です。", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "絶対値が 2^53 以上の数値リテラルは大きすぎるため、整数として正確に表現できません。", "Numeric_separators_are_not_allowed_here_6188": "数値の区切り記号は、ここでは使用できません。", "Object_is_of_type_unknown_2571": "オブジェクト型は 'unknown' です。", "Object_is_possibly_null_2531": "オブジェクトは 'null' である可能性があります。", "Object_is_possibly_null_or_undefined_2533": "オブジェクトは 'null' か 'undefined' である可能性があります。", "Object_is_possibly_undefined_2532": "オブジェクトは 'undefined' である可能性があります。", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "オブジェクト リテラルは既知のプロパティのみ指定できます。'{0}' は型 '{1}' に存在しません。", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "オブジェクト リテラルで指定できるのは既知のプロパティのみですが、'{0}' は型 '{1}' に存在しません。書こうとしたのは '{2}' ですか?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "オブジェクト リテラルのプロパティ '{0}' の型は暗黙的に '{1}' になります。", "Objects_that_contain_shorthand_properties_can_t_be_inferred_with_isolatedDeclarations_9016": "短縮形プロパティを含むオブジェクトは、--isolatedDeclarations では推論できません。", "Objects_that_contain_spread_assignments_can_t_be_inferred_with_isolatedDeclarations_9015": "スプレッド割り当てを含むオブジェクトを --isolatedDeclarations と共に推論することはできません。", "Octal_digit_expected_1178": "8 進の数字が必要です。", "Octal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_If_this_was_intended__1536": "文字クラスでは、8 進数のエスケープ シーケンスと前方参照を使用することはできません。これがエスケープ シーケンスとして意図されていた場合は、代わりに構文 '{0}' を使用してください。", "Octal_escape_sequences_are_not_allowed_Use_the_syntax_0_1487": "8 進数のエスケープ シーケンスは使用できません。構文 '{0}' を使用してください。", "Octal_literals_are_not_allowed_Use_the_syntax_0_1121": "8 進数リテラルは使用できません。構文 '{0}' を使用してください。", "One_value_of_0_1_is_the_string_2_and_the_other_is_assumed_to_be_an_unknown_numeric_value_4126": "'{0}.{1}' の 1 つの値は文字列 '{2}' で、もう一方の値は不明な数値であると見なされます。", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "'for...in' ステートメントで使用できる変数宣言は 1 つのみです。", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "'for...of' ステートメントで使用できる変数宣言は 1 つのみです。", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "'new' キーワードを指定して呼び出せるのは void 関数のみです。", "Only_ambient_modules_can_use_quoted_names_1035": "引用符付きの名前を使用できるのはアンビエント モジュールのみです。", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "--{0} と共にサポートされるのは 'amd' モジュールと 'system' モジュールのみです。", "Only_const_arrays_can_be_inferred_with_isolatedDeclarations_9017": "--isolatedDeclarations を使用して推論できるのは const 配列のみです。", "Only_emit_d_ts_declaration_files_6014": "'.d.ts' 宣言ファイルのみを生成します。", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "出力の d.ts ファイルのみで、JavaScript ファイルは対象ではありません。", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "'super' キーワードを使用してアクセスできるのは、基底クラスのパブリック メソッドと保護されたメソッドのみです。", "Operator_0_cannot_be_applied_to_type_1_2736": "演算子 '{0}' は型 '{1}' に適用できません。", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "演算子 '{0}' を型 '{1}' および '{2}' に適用することはできません。", "Operators_must_not_be_mixed_within_a_character_class_Wrap_it_in_a_nested_class_instead_1519": "演算子は文字クラス内で混在してはなりません。入れ子になったクラスでラップしてください。", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "編集時に複数プロジェクト参照のチェックからプロジェクトをオプトアウトします。", "Option_0_1_has_been_removed_Please_remove_it_from_your_configuration_5108": "オプション '{0}={1}' が削除されました。構成から削除してください。", "Option_0_1_is_deprecated_and_will_stop_functioning_in_TypeScript_2_Specify_compilerOption_ignoreDepr_5107": "オプション '{0}={1}' は非推奨であり、TypeScript {2} で機能しなくなります。compilerOption '\"ignoreDeprecations\": \"{3}\"' を指定して、このエラーを無音にします。", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "オプション '{0}' は、'tsconfig.json' ファイルで指定することか、コマンド ラインで 'false' または 'null' に設定することしかできません。", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "オプション '{0}' は、'tsconfig.json' ファイルで指定することか、コマンド ラインで 'null' に設定することしかできません。", "Option_0_can_only_be_specified_on_command_line_6266": "オプション '{0}' はコマンド ラインでのみ指定できます。", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "オプション '{0} を使用できるのは、オプション '--inlineSourceMap' またはオプション '--sourceMap' のいずれかを指定した場合のみです。", "Option_0_can_only_be_used_when_moduleResolution_is_set_to_node16_nodenext_or_bundler_5098": "オプション '{0}' は、'moduleResolution' が 'node16'、'nodenext'、または 'bundler' に設定されている場合にのみ使用できます。", "Option_0_can_only_be_used_when_module_is_set_to_preserve_or_to_es2015_or_later_5095": "オプション '{0}' は、'module' が 'preserve' または 'es2015' 以降に設定されている場合にのみ使用できます。", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "オプション 'jsx' が '{1}' の場合、オプション '{0}' を指定することはできません。", "Option_0_cannot_be_specified_with_option_1_5053": "オプション '{0}' をオプション '{1}' とともに指定することはできません。", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "オプション '{1}' を指定せずに、オプション '{0}' を指定することはできません。", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "オプション '{1}' またはオプション '{2}' を指定せずに、オプション '{0}' を指定することはできません。", "Option_0_has_been_removed_Please_remove_it_from_your_configuration_5102": "オプション '{0}' が削除されました。構成から削除してください。", "Option_0_is_deprecated_and_will_stop_functioning_in_TypeScript_1_Specify_compilerOption_ignoreDeprec_5101": "オプション '{0}' は非推奨であり、TypeScript {1} で機能しなくなります。compilerOption '\"ignoreDeprecations\": \"{2}\"' を指定して、このエラーを無音にします。", "Option_0_is_redundant_and_cannot_be_specified_with_option_1_5104": "オプション '{0}' は冗長であり、オプション '{1}' とともに指定することができません。", "Option_allowImportingTsExtensions_can_only_be_used_when_either_noEmit_or_emitDeclarationOnly_is_set_5096": "オプション 'allowImportingTsExtensions' は、'noEmit' または 'emitDeclarationOnly' が設定されている場合にのみ使用できます。", "Option_build_must_be_the_first_command_line_argument_6369": "オプション '--build' は最初のコマンド ライン引数である必要があります。", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "オプション '--incremental' は、tsconfig を使用して指定して単一ファイルに出力するか、オプション '--tsBuildInfoFile' が指定された場合にのみ指定することができます。", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "オプション 'isolatedModules' は、オプション '--module' が指定されているか、オプション 'target' が 'ES2015' 以上であるかのいずれかの場合でのみ使用できます。", "Option_moduleResolution_must_be_set_to_0_or_left_unspecified_when_option_module_is_set_to_1_5109": "オプション 'module' が '{1}' に設定されている場合は、オプション 'moduleResolution' を '{0}' (または未指定のままに) に設定する必要があります。", "Option_module_must_be_set_to_0_when_option_moduleResolution_is_set_to_1_5110": "オプション 'moduleResolution' が '{1}' に設定されている場合は、オプション 'module' を '{0}' に設定する必要があります。", "Option_preserveConstEnums_cannot_be_disabled_when_0_is_enabled_5091": "'{0}' が有効になっている場合、オプション 'preserveConstEnums' を無効にすることはできません。", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "オプション 'project' をコマンド ライン上でソース ファイルと一緒に指定することはできません。", "Option_resolveJsonModule_cannot_be_specified_when_moduleResolution_is_set_to_classic_5070": "'moduleResolution' が 'classic' に設定されている場合、オプション '--resolveJsonModule' を指定できません。", "Option_resolveJsonModule_cannot_be_specified_when_module_is_set_to_none_system_or_umd_5071": "'module' が 'none'、'system'、または 'umd' に設定されている場合、オプション '--resolveJsonModule' を指定することはできません。", "Option_verbatimModuleSyntax_cannot_be_used_when_module_is_set_to_UMD_AMD_or_System_5105": "'module' が 'UMD'、'AMD'、または 'System' に設定されている場合、オプション 'verbatimModuleSyntax' は使用できません。", "Options_0_and_1_cannot_be_combined_6370": "オプション '{0}' と '{1}' を組み合わせることはできません。", "Options_Colon_6027": "オプション:", "Output_Formatting_6256": "出力データ形式", "Output_compiler_performance_information_after_building_6615": "ビルド後にコンパイラのパフォーマンス情報を出力します。", "Output_directory_for_generated_declaration_files_6166": "生成された宣言ファイルの出力ディレクトリ。", "Output_file_0_has_not_been_built_from_source_file_1_6305": "出力ファイル '{0}' はソース ファイル '{1}' からビルドされていません。", "Output_from_referenced_project_0_included_because_1_specified_1411": "'{1}' が指定されたため、参照先プロジェクト '{0}' から出力がインクルードされました", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "'--module' が 'none' として指定されたため、参照先プロジェクト '{0}' から出力がインクルードされました", "Output_more_detailed_compiler_performance_information_after_building_6632": "ビルド後により詳しいコンパイラのパフォーマンス情報を出力します。", "Overload_0_of_1_2_gave_the_following_error_2772": "{1} 中 {0} のオーバーロード, '{2}' により、次のエラーが発生しました。", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "オーバーロードのシグネチャはすべてが抽象または非抽象である必要があります。", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "オーバーロードのシグネチャは、すべてアンビエントであるか、すべてアンビエントでないかのどちらかである必要があります。", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "オーバーロードのシグネチャはすべてがエクスポート済みであるか、またはエクスポート済みでない必要があります。", "Overload_signatures_must_all_be_optional_or_required_2386": "オーバーロードのシグネチャは、すべて省略可能であるか、すべて必須であるかのどちらかである必要があります。", "Overload_signatures_must_all_be_public_private_or_protected_2385": "オーバーロードのシグネチャはすべて、public、private、または protected でなければなりません。", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "パラメーター '{0}' はその後で宣言された識別子 '{1}' を参照できません。", "Parameter_0_cannot_reference_itself_2372": "パラメーター '{0}' は、それ自体を参照できません。", "Parameter_0_implicitly_has_an_1_type_7006": "パラメーター '{0}' の型は暗黙的に '{1}' になります。", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "パラメーター '{0}' の型は暗黙的に '{1}' になっていますが、使い方からより良い型を推論できます。", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "パラメーター '{0}' がパラメーター '{1}' と同じ位置にありません。", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "アクセサーのパラメーター '{0}' が外部モジュール '{2}' からの名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "アクセサーのパラメーター '{0}' が、プライベート モジュール '{2}' からの名前 '{1}' を持っているか、使用しています。", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "アクセサーのパラメーター '{0}' がプライベート名 '{1}' を持っているか、使用しています。", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "エクスポートされたインターフェイスの呼び出しシグネチャのパラメーター '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "エクスポートされたインターフェイスの呼び出しシグネチャのパラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "エクスポートされたクラスのコンストラクターのパラメーター '{0}' が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "エクスポートされたクラスのコンストラクターのパラメーター '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "エクスポートされたクラスのコンストラクターのパラメーター '{0}' がプライベート名 '{1}' を持っているか、使用しています。", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "エクスポートされたインターフェイスのコンストラクター シグネチャのパラメーター '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "エクスポートされたインターフェイスのコンストラクター シグネチャのパラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "エクスポートされた関数のパラメーター '{0}' が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "エクスポートされた関数のパラメーター '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "エクスポートされた関数のパラメーター '{0}' がプライベート名 '{1}' を持っているか、使用しています。", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "エクスポートされたインターフェイスのインデックス シグネチャのパラメーター '{0}' で、プライベート モジュール '{2}' の名前 '{1}' が指定されているか使用されています。", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "エクスポートされたインターフェイスのインデックス シグネチャのパラメーター '{0}' で、プライベート名 '{1}' が指定されているか使用されています。", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "エクスポートされたインターフェイスのメソッドのパラメーター '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "エクスポートされたインターフェイスのメソッドのパラメーター '{0}' がプライベート名 '{1}' を持っているか、使用しています。", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "エクスポートされたクラスのパブリック メソッドのパラメーター '{0}' が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "エクスポートされたクラスのパブリック メソッドのパラメーター '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "エクスポートされたクラスのパブリック メソッドのパラメーター '{0}' がプライベート名 '{1}' を持っているか、使用しています。", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "エクスポートされたクラスのパブリック静的メソッドのパラメーター '{0}' が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "エクスポートされたクラスのパブリック静的メソッドのパラメーター '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "エクスポートされたクラスのパブリック静的メソッドのパラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Parameter_cannot_have_question_mark_and_initializer_1015": "パラメーターに疑問符および初期化子を指定することはできません。", "Parameter_declaration_expected_1138": "パラメーター宣言が必要です。", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "パラメーターに名前はありますが、型がありません。'{0}: {1}' を意図していましたか?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "パラメーター修飾子は TypeScript ファイルでのみ使用できます。", "Parameter_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9011": "パラメーターには、--isolatedDeclarations を含む明示的な型注釈が必要です。", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "エクスポートされたクラスのパブリック セッター '{0}' のパラメーター型が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "エクスポートされたクラスのパブリック セッター '{0}' のパラメーター型が、プライベート名 '{1}' を持っているか、使用しています。", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "エクスポートされたクラスのパブリック静的セッター '{0}' のパラメーター型が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "エクスポートされたクラスのパブリック静的セッター '{0}' のパラメーター型が、プライベート名 '{1}' を持っているか、使用しています。", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "厳格モードで解析してソース ファイルごとに \"use strict\" を生成します。", "Part_of_files_list_in_tsconfig_json_1409": "tsconfig.json の 'files' リストの一部", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "パターン '{0}' に使用できる '*' 文字は最大で 1 つです。", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "'--diagnostics' または '--extendedDiagnostics' のパフォーマンスのタイミングは、このセッションでは使用できません。Web パフォーマンス API のネイティブ実装が見つかりませんでした。", "Platform_specific_6912": "プラットフォーム固有", "Prefix_0_with_an_underscore_90025": "アンダースコアを含むプレフィックス '{0}'", "Prefix_all_incorrect_property_declarations_with_declare_95095": "すべての正しくないプロパティ宣言の前に 'declare' を付ける", "Prefix_all_unused_declarations_with_where_possible_95025": "可能な場合は、使用されていないすべての宣言にプレフィックスとして '_' を付けます", "Prefix_with_declare_95094": "'declare' を前に付ける", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "さもなければ削除されてしまう JavaScript のアウトプット中の使われていないインポートされた値を保持します。", "Print_all_of_the_files_read_during_the_compilation_6653": "コンパイル時に読み取られたすべてのファイルを出力します。", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "コンパイル時に読み取られたファイルを、それが含まれる理由と共に出力します。", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "ファイルの名前と、それらがコンパイルに含まれている理由を書き出します。", "Print_names_of_files_part_of_the_compilation_6155": "コンパイルの一環としてファイルの名前を書き出します。", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "コンパイルの一部であるファイルの名前を出力してから、処理を停止します。", "Print_names_of_generated_files_part_of_the_compilation_6154": "コンパイルの一環として生成されたファイル名を書き出します。", "Print_the_compiler_s_version_6019": "コンパイラのバージョンを表示します。", "Print_the_final_configuration_instead_of_building_1350": "ビルドを実行するのではなく、最終的な構成を出力します。", "Print_the_names_of_emitted_files_after_a_compilation_6652": "コンパイル後に生成されたファイルの名前を出力します。", "Print_this_message_6017": "このメッセージを表示します。", "Private_accessor_was_defined_without_a_getter_2806": "ゲッターなしでプライベート アクセサーが定義されました。", "Private_field_0_must_be_declared_in_an_enclosing_class_1111": "プライベート フィールド '{0}' は、エンクロージング クラスで宣言する必要があります。", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "変数宣言では、private 識別子は許可されていません。", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "private 識別子は、クラス本体の外では許可されていません。", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "プライベート識別子はクラス本体でのみ許可され、クラス メンバー宣言、プロパティ アクセス、または 'in' 式の左側でのみ使用できます", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "private 識別子は ECMAScript 2015 以上をターゲットにする場合にのみ使用できます。", "Private_identifiers_cannot_be_used_as_parameters_18009": "private 識別子はパラメーターとして使用できません。", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "private または protected メンバー '{0}' には、型パラメーターではアクセスできません。", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Project '{0}' can't be built because its dependency '{1}' has errors", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Project '{0}' can't be built because its dependency '{1}' was not built", "Project_0_is_being_forcibly_rebuilt_6388": "プロジェクト '{0}' が強制的にリビルドされています", "Project_0_is_out_of_date_because_1_6420": "{1}のため、プロジェクト '{0}' は古くなっています。", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_file_2_was_root_file_of_compilation_6412": "buildinfo ファイル '{1}' は、ファイル '{2}' がコンパイルのルート ファイルでしたが、それ以上はないことを示しているため、プロジェクト '{0}' は最新ではありません。", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_program_needs_to_report_errors_6419": "buildinfo ファイル '{1}' はプログラムがエラーを報告する必要があることを示しているため、プロジェクト '{0}' は最新ではありません。", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "buildinfo ファイルの '{1}' により、一部の変更が生成されなかったことが示されているため、プロジェクトの '{0}' は最新ではありません", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_there_is_change_in_compilerOptions_6406": "buildinfo ファイル '{1}' は compilerOptions に変更があることを示しているため、プロジェクト '{0}' は最新ではありません", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "プロジェクト '{0}' はその依存関係 '{1}' が古いため最新の状態ではありません", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "プロジェクト '{0}' は出力 '{1}' が入力 '{2}' より古いため最新の状態ではありません", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "プロジェクト '{0}' は出力ファイル '{1}' が存在しないため最新の状態ではありません", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "プロジェクト '{0}' の出力が現在のバージョン '{2}' と異なるバージョン '{1}' で生成されているため、このプロジェクトは最新の状態ではありません", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "ファイル '{1}' の読み取り中にエラーが発生したため、プロジェクト '{0}' は最新ではありません", "Project_0_is_up_to_date_6361": "プロジェクト '{0}' は最新の状態です", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "プロジェクト '{0}' は最新の入力 '{1}' が出力 '{2}' より古いため最新の状態です", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "プロジェクト '{0}' は最新ですが、入力ファイルよりも古い出力ファイルのタイムスタンプを更新する必要があります", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "プロジェクト '{0}' はその依存関係からの .d.ts ファイルで最新の状態です", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "プロジェクト参照が円グラフを形成できません。循環が検出されました: {0}", "Projects_6255": "プロジェクト", "Projects_in_this_build_Colon_0_6355": "このビルドのプロジェクト: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "'accessor' 修飾子を持つプロパティは、ECMAScript 2015 以降を対象とする場合にのみ使用できます。", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "メソッド '{0}' は abstract に指定されているため、初期化子を含めることができません。", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "プロパティ '{0}' はインデックス シグネチャに基づいているため、['{0}'] を使用してアクセスする必要があります。", "Property_0_does_not_exist_on_type_1_2339": "プロパティ '{0}' は型 '{1}' に存在しません。", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "プロパティ '{0}' は型 '{1}' に存在していません。'{2}' ですか?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "プロパティ '{0}' は型 '{1}' には存在しません。代わりに静的メンバー '{2}' にアクセスしようとしていましたか?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "プロパティ '{0}' が型 '{1}' に存在しません。ターゲット ライブラリを変更する必要がありますか? 'lib' コンパイラ オプションを '{2}' 以降に変更してみてください。", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "プロパティ ' {0} ' は型 ' {1} ' に存在しません。' lib ' コンパイラ オプションを ' dom ' を含むように変更してみてください。", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "プロパティ '{0}' に初期化子がなく、クラスの静的ブロックで明確に割り当てられていません。", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "プロパティ '{0}' に初期化子がなく、コンストラクターで明確に割り当てられていません。", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "プロパティ '{0}' には型 'any' が暗黙的に設定されています。get アクセサーには戻り値の型の注釈がないためです。", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "プロパティ '{0}' には型 'any' が暗黙的に設定されています。set アクセサーにはパラメーター型の注釈がないためです。", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "プロパティ '{0}' の型は暗黙的に 'any' ですが、その get アクセサーのために、使い方からより良い型を推論する場合があります。", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "プロパティ '{0}' の型は暗黙的に 'any' になっていますが、その set アクセサーのより良い型を使い方から推論できます。", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "型 '{1}' のプロパティ '{0}' を基本データ型 '{2}' の同じプロパティに割り当てることはできません。", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "型 '{1}' のプロパティ '{0}' を型 '{2}' に割り当てることはできません。", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "型 '{1}' のプロパティ '{0}' は、型 '{2}' 内からアクセスできない別のメンバーを参照しています。", "Property_0_is_declared_but_its_value_is_never_read_6138": "プロパティ '{0}' が宣言されていますが、その値が読み取られることはありません。", "Property_0_is_incompatible_with_index_signature_2530": "プロパティ '{0}' はインデックス シグネチャと互換性がありません。", "Property_0_is_missing_in_type_1_2324": "型 '{1}' にプロパティ '{0}' がありません。", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "プロパティ '{0}' は型 '{1}' にありませんが、型 '{2}' では必須です。", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "プロパティ '{0}' には private 識別子が指定されているため、クラス '{1}' の外部ではアクセスできません。", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "プロパティ '{0}' は型 '{1}' では省略可能ですが、型 '{2}' では必須です。", "Property_0_is_private_and_only_accessible_within_class_1_2341": "プロパティ '{0}' はプライベートで、クラス '{1}' 内でのみアクセスできます。", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "プロパティ '{0}' は型 '{1}' ではプライベートですが、型 '{2}' ではプライベートではありません。", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "プロパティ '{0}' は保護されており、クラス '{1}' のインスタンスを通じてのみアクセスできます。これは、クラス '{2}' のインスタンスです。", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "プロパティ '{0}' は保護されているため、クラス '{1}' とそのサブクラス内でのみアクセスできます。", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "プロパティ '{0}' は保護されていますが、型 '{1}' は '{2}' から派生したクラスではありません。", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "プロパティ '{0}' は型 '{1}' では保護されていますが、型 '{2}' ではパブリックです。", "Property_0_is_used_before_being_assigned_2565": "プロパティ '{0}' は割り当てられる前に使用されています。", "Property_0_is_used_before_its_initialization_2729": "プロパティ '{0}' が初期化前に使用されています。", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "プロパティ '{0}' は型 '{1}' に存在していない可能性があります。'{2}' ですか?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "JSX のスプレッド属性のプロパティ '{0}' をターゲット プロパティに割り当てることはできません。", "Property_0_of_exported_anonymous_class_type_may_not_be_private_or_protected_4094": "エクスポートされた匿名クラス型のプロパティ '{0}' は、プライベートでないか保護されていない可能性があります。", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "エクスポートされたインターフェイスのプロパティ '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "エクスポートされたインターフェイスのプロパティ '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "型 '{1}' のプロパティ '{0}' は'{2}' インデックス型 '{3}' に割り当てることはできません。", "Property_0_was_also_declared_here_2733": "ここではプロパティ '{0}' も宣言されています。", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "プロパティ '{0}' は、'{1}' の基底プロパティを上書きします。これが意図的である場合は初期化子を追加してください。そうでなければ、'declare' 修飾子を追加するか、冗長な宣言を削除してください。", "Property_assignment_expected_1136": "プロパティの代入が必要です。", "Property_destructuring_pattern_expected_1180": "プロパティの非構造化パターンが必要です。", "Property_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9012": "プロパティには、--isolatedDeclarations を含む明示的な型注釈が必要です。", "Property_or_signature_expected_1131": "プロパティまたはシグネチャが必要です。", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "プロパティ値には、文字列リテラル、数値リテラル、'true'、'false'、'null'、オブジェクト リテラルまたは配列リテラルのみ使用できます。", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_6179": "'for-of'、spread、destructuring で 'ES5' を対象とする場合は、iterables を完全にサポートします。", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "エクスポートされたクラスのパブリック メソッド '{0}' が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "エクスポートされたクラスのパブリック メソッド '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "エクスポートされたクラスのパブリック メソッド '{0}' がプライベート名 '{1}' を持っているか、使用しています。", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "エクスポートされたクラスのパブリック プロパティ '{0}' が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "エクスポートされたクラスのパブリック プロパティ '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "エクスポートされたクラスのパブリック プロパティ '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "エクスポートされたクラスのパブリック静的メソッド '{0}' が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "エクスポートされたクラスのパブリック静的メソッド '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "エクスポートされたクラスのパブリック静的メソッド '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "エクスポートされたクラスのパブリック静的プロパティ '{0}' が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "エクスポートされたクラスのパブリック静的プロパティ '{0}' が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "エクスポートされたクラスのパブリック静的プロパティ '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "先頭に '@param {object} {1}' がない場合、修飾名 '{0}' は許可されません。", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "関数パラメーターが読み取られていないときに、エラーを発生させます。", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "暗黙的な 'any' 型を含む式と宣言に関するエラーを発生させます。", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "暗黙的な 'any' 型を持つ 'this' 式でエラーが発生します。", "Range_out_of_order_in_character_class_1517": "文字クラスの順序が正しくありません。", "Re_exporting_a_type_when_0_is_enabled_requires_using_export_type_1205": "'{0}' が有効な場合に型を再エクスポートするには、'export type' を使用する必要があります。", "React_components_cannot_include_JSX_namespace_names_2639": "React コンポーネントに JSX 名前空間名を含めることはできません", "Redirect_output_structure_to_the_directory_6006": "ディレクトリへ出力構造をリダイレクトします。", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "TypeScript によって自動的に読み込まれるプロジェクトの数を減らします。", "Referenced_project_0_may_not_disable_emit_6310": "参照されたプロジェクト '{0}' は、生成を無効にできません。", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "参照されているプロジェクト '{0}' には、設定 \"composite\": true が必要です。", "Referenced_via_0_from_file_1_1400": "ファイル '{1}' から '{0}' を介して参照されています", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2834": "相対インポート パスでは、'--moduleResolution' が 'node16' または 'nodenext' である場合、ECMAScript インポートに明示的なファイル拡張子が必要です。インポート パスに拡張機能を追加することを検討してください。", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2835": "相対インポート パスでは、'--moduleResolution' が 'node16' または 'nodenext' である場合、ECMAScript インポートに明示的なファイル拡張子が必要です。'{0}' を意図していましたか?", "Remove_a_list_of_directories_from_the_watch_process_6628": "ウォッチ プロセスからディレクトリの一覧を削除します。", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "ウォッチ モードの処理からファイルの一覧を削除します。", "Remove_all_unnecessary_override_modifiers_95163": "不要な 'override' 修飾子をすべて削除", "Remove_all_unnecessary_uses_of_await_95087": "不要な 'await' の使用をすべて削除する", "Remove_all_unreachable_code_95051": "到達できないコードをすべて削除します", "Remove_all_unused_labels_95054": "すべての未使用のラベルを削除します", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "関連する問題のあるすべてのアロー関数本体から中かっこを削除します", "Remove_braces_from_arrow_function_95060": "アロー関数から中かっこを削除します", "Remove_braces_from_arrow_function_body_95112": "アロー関数本体から中かっこを削除します", "Remove_import_from_0_90005": "'{0}' からのインポートを削除", "Remove_override_modifier_95161": "'override ' 修飾子の削除", "Remove_parentheses_95126": "かっこの削除", "Remove_template_tag_90011": "テンプレート タグを削除する", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "TypeScript 言語サーバーの JavaScript ファイルのソース コードの合計サイズについて 20 MB の上限を削除します。", "Remove_type_from_import_declaration_from_0_90055": "\"{0}\" からインポート宣言から `type` を削除します", "Remove_type_from_import_of_0_from_1_90056": "\"{1}\" から `{0}` のインポートから `type` を削除します", "Remove_type_parameters_90012": "型パラメーターを削除する", "Remove_unnecessary_await_95086": "不要な 'await' を削除する", "Remove_unreachable_code_95050": "到達できないコードを削除します", "Remove_unused_declaration_for_Colon_0_90004": "'{0}' に対する使用されていない宣言を削除する", "Remove_unused_declarations_for_Colon_0_90041": "'{0}' に対する使用されていない宣言を削除してください", "Remove_unused_destructuring_declaration_90039": "使用されていない非構造化宣言を削除してください", "Remove_unused_label_95053": "未使用のラベルを削除します", "Remove_variable_statement_90010": "変数のステートメントを削除します", "Rename_param_tag_name_0_to_1_95173": "'@param' タグ名の名前を '{0}' から '{1}' に変更します", "Replace_0_with_Promise_1_90036": "'{0}' を 'Promise<{1}>' に置き換える", "Replace_all_unused_infer_with_unknown_90031": "未使用の 'infer' をすべて 'unknown' に置き換える", "Replace_import_with_0_95015": "インポートを '{0}' に置換します。", "Replace_infer_0_with_unknown_90030": "'infer {0}' を 'unknown' に置き換える", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "関数の一部のコード パスが値を返さない場合にエラーを報告します。", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "switch ステートメントに case のフォールスルーがある場合にエラーを報告します。", "Report_errors_in_js_files_8019": ".js ファイルのエラーを報告します。", "Report_errors_on_unused_locals_6134": "使用されていないローカルに関するエラーを報告します。", "Report_errors_on_unused_parameters_6135": "使用されていないパラメーターに関するエラーを報告します。", "Require_sufficient_annotation_on_exports_so_other_tools_can_trivially_generate_declaration_files_6719": "他のツールが宣言ファイルを簡単に生成できるように、エクスポートに十分な注釈を必要とします。", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "要素アクセスを使用するには、インデックス シグネチャからの宣言されていないプロパティが必要です。", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "必須の型パラメーターの後に、オプションの型パラメーターを続けることはできません。", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "モジュール '{0}' の解決が場所 '{1}' のキャッシュに見つかりました。", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "種類参照指令 '{0}' の解決策は、場所 '{1}' のキャッシュには見つかりませんでした。", "Resolution_of_non_relative_name_failed_trying_with_modern_Node_resolution_features_disabled_to_see_i_6277": "非相対名の解決に失敗しました。npm ライブラリで構成の更新が必要かどうかを確認するために、最新のノード解決機能を無効にしています。", "Resolution_of_non_relative_name_failed_trying_with_moduleResolution_bundler_to_see_if_project_may_ne_6279": "非相対名の解決に失敗しました。'--moduleResolution bundler' を使用して、プロジェクトで構成の更新が必要かどうかを確認しています。", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "'keyof' を文字列値のプロパティ名のみに解決します (数字または記号なし)。", "Resolved_under_condition_0_6414": "条件 '{0}' で解決されました。", "Resolving_in_0_mode_with_conditions_1_6402": "条件 {1} を使用して {0} モードで解決しています。", "Resolving_module_0_from_1_6086": "======== '{1}' からモジュール '{0}' を解決しています。========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "ベース URL '{1}' - '{2}' に相対するモジュール名 '{0}' を解決しています。", "Resolving_real_path_for_0_result_1_6130": "'{0}' の実際のパスを解決しています。結果は '{1}' です。", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== ファイル '{1}' のある種類参照指令 '{0}' の解決 ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== 型参照ディレクティブ '{0}' を解決しています。それを含むファイル '{1}'、ルート ディレクトリ '{2}'。========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== 型参照ディレクティブ '{0}' を解決しています。それを含むファイル '{1}'、ルート ディレクトリは未設定。========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== 型参照ディレクティブ '{0}' を解決しています。それを含むファイルは未設定、ルート ディレクトリ '{1}'。========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== 型参照ディレクティブ '{0}' を解決しています。それを含むファイルは未設定、ルート ディレクトリは未設定。========", "Resolving_type_reference_directive_for_program_that_specifies_custom_typeRoots_skipping_lookup_in_no_6265": "カスタム typeRoots を指定するプログラムの型参照ディレクティブを解決しています。'node_modules' フォルダーでの参照をスキップします。", "Resolving_with_primary_search_path_0_6121": "プライマリ検索パス '{0}' で解決しています。", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Rest パラメーター '{0}' の型は暗黙的に 'any[]' になります。", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "rest パラメーター '{0}' の型は暗黙的に 'any[]' 型ですが、使い方からより良い型を推論する場合があります。", "Rest_types_may_only_be_created_from_object_types_2700": "rest 型はオブジェクトの種類からのみ作成できます。", "Return_type_annotation_circularly_references_itself_2577": "戻り値の型の注釈は、それ自身を循環参照します。", "Return_type_must_be_inferred_from_a_function_95149": "戻り値の型は関数から推論される必要があります", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "エクスポートされたインターフェイスの呼び出しシグネチャの戻り値の型が、プライベート モジュール '{1}' の名前 '{0}' を持っているか、使用しています。", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "エクスポートされたインターフェイスの呼び出しシグネチャの戻り値の型が、プライベート名 '{0}' を持っているか、使用しています。", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "エクスポートされたインターフェイスのコンストラクター シグネチャの戻り値の型が、プライベート モジュール '{1}' の名前 '{0}' を持っているか、使用しています。", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "エクスポートされたインターフェイスのコンストラクター シグネチャの戻り値の型が、プライベート名 '{0}' を持っているか、使用しています。", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "コンストラクター シグネチャの戻り値の型は、クラスのインスタンス型に割り当て可能でなければなりません。", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "エクスポートされた関数の戻り値の型が外部モジュール {1} の名前 '{0}' を持っているか使用していますが、名前を指定することはできません。", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "エクスポートされた関数の戻り値の型が、プライベート モジュール '{1}' の名前 '{0}' を持っているか、使用しています。", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "エクスポートされた関数の戻り値の型が、プライベート名 '{0}' を持っているか、使用しています。", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "エクスポートされたインターフェイスのインデックス シグネチャの戻り値の型が、プライベート モジュール '{1}' の名前 '{0}' を持っているか、使用しています。", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "エクスポートされたインターフェイスのインデックス シグネチャの戻り値の型が、プライベート名 '{0}' を持っているか、使用しています。", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "エクスポートされたインターフェイスのメソッドの戻り値の型が、プライベート モジュール '{1}' の名前 '{0}' を持っているか、使用しています。", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "エクスポートされたインターフェイスのメソッドの戻り値の型が、プライベート名 '{0}' を持っているか、使用しています。", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "エクスポートされたクラスのパブリック ゲッター '{0}' の戻り値の型が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "エクスポートされたクラスのパブリック ゲッター '{0}' の戻り値の型が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "エクスポートされたクラスのパブリック ゲッター '{0}' の戻り値の型が、プライベート名 '{1}' を持っているか、使用しています。", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "エクスポートされたクラスのパブリック メソッドの戻り値の型が外部モジュール {1} の名前 '{0}' を持っているか使用していますが、名前を指定することはできません。", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "エクスポートされたクラスのパブリック メソッドの戻り値の型が、プライベート モジュール '{1}' の名前 '{0}' を持っているか、使用しています。", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "エクスポートされたクラスのパブリック メソッドの戻り値の型が、プライベート名 '{0}' を持っているか、使用しています。", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "エクスポートされたクラスのパブリック静的ゲッター '{0}' の戻り値の型が外部モジュール {2} の名前 '{1}' を持っているか使用していますが、名前を指定することはできません。", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "エクスポートされたクラスのパブリック静的ゲッター '{0}' の戻り値の型が、プライベート モジュール '{2}' の名前 '{1}' を持っているか、使用しています。", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "エクスポートされたクラスのパブリック静的ゲッター '{0}' の戻り値の型が、プライベート名 '{1}' を持っているか、使用しています。", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "エクスポートされたクラスのパブリック静的メソッドの戻り値の型が外部モジュール {1} の名前 '{0}' を持っているか使用していますが、名前を指定することはできません。", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "エクスポートされたクラスのパブリック静的メソッドの戻り値の型が、プライベート モジュール '{1}' の名前 '{0}' を持っているか、使用しています。", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "エクスポートされたクラスのパブリック静的メソッドの戻り値の型が、プライベート名 '{0}' を持っているか、使用しています。", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "場所 '{2}' のキャッシュにあった '{1}' からモジュール '{0}' の解決策を再利用しましたが、解決できませんでした。", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "場所 '{2}' のキャッシュにあった '{1}' からモジュール '{0}' の解決策を再利用すると、'{3}' に正常に解決されました。", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "場所 '{2}' からキャッシュにあった '{1}' からモジュール '{0}' の解決策を再利用すると、パッケージ ID '{4}' の '{3}' に正常に解決されました。", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "古いプログラムの '{1}' からモジュール '{0}' の解決策を再利用しようとしましたが、解決されませんでした。", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "古いプログラムの '{1}' からモジュール '{0}' の解決策を再利用すると、'{2}' に正常に解決されました。", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "古いプログラムの '{1}' からモジュール '{0}' の解決策を再利用すると、パッケージ ID '{3}' の '{2}' に正常に解決されました。", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "場所 '{2}' からキャッシュにあった '{1}' からタイプ リファレンス ディレクティブ '{0}' の解決策を再利用しましたが、解決できませんでした。", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "場所 '{2}' からキャッシュにあった '{1}' からタイプ リファレンス ディレクティブ '{0}' の解決策を再利用すると、'{3}' に正常に解決されました。", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "場所 '{2}' からキャッシュにあった '{1}' からタイプ リファレンス ディレクティブ '{0}' の解決策を再利用すると、パッケージ ID '{4}' の '{3}' に正常に解決されました。", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "古いプログラムの '{1}' からタイプ リファレンス ディレクティブ '{0}' の解決策を再利用しましたが、解決できませんでした。", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "古いプログラムの '{1}' からタイプ リファレンス ディレクティブ '{0}' の解決策を再利用すると、'{2}' に正常に解決されました。", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "古いプログラムの '{1}' からタイプ リファレンス ディレクティブ '{0}' の解決策を再利用すると、パッケージ ID '{3}' の '{2}' に正常に解決されました。", "Rewrite_all_as_indexed_access_types_95034": "すべてをインデックス付きアクセス型として書き換えます", "Rewrite_as_the_indexed_access_type_0_90026": "インデックス付きのアクセスの種類 '{0}' として書き換える", "Rewrite_ts_tsx_mts_and_cts_file_extensions_in_relative_import_paths_to_their_JavaScript_equivalent_i_6421": "相対インポート パスの '.ts'、'.tsx'、'.mts'、および '.cts' ファイル拡張子を、出力ファイルの JavaScript と同等の拡張子に書き換えます。", "Right_operand_of_is_unreachable_because_the_left_operand_is_never_nullish_2869": "?? の右オペランド左オペランドが NULL 値になることがないため、到達できません。", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "ルート ディレクトリを決定できません。プライマリ検索パスをスキップします。", "Root_file_specified_for_compilation_1427": "コンパイル用に指定されたルート ファイル", "STRATEGY_6039": "戦略", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "プロジェクトのインクリメンタル コンパイルを可能にするには、.tsbuildinfo ファイルを保存します。", "Saw_non_matching_condition_0_6405": "一致しない条件 '{0}' がありました。", "Scoped_package_detected_looking_in_0_6182": "'{0}' 内を検索して、スコープ パッケージが検出されました", "Searching_all_ancestor_node_modules_directories_for_fallback_extensions_Colon_0_6418": "すべての先祖 node_modules ディレクトリでフォールバック拡張子を検索しています: {0}。", "Searching_all_ancestor_node_modules_directories_for_preferred_extensions_Colon_0_6417": "すべての先祖 node_modules ディレクトリで優先する拡張子を検索しています: {0}。", "Selection_is_not_a_valid_statement_or_statements_95155": "選択内容は有効なステートメントではありません", "Selection_is_not_a_valid_type_node_95133": "選択は有効な型ノードではありません", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "生成された JavaScript の JavaScript 言語バージョンを設定し、互換性のあるライブラリ宣言を含めます。", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "TypeScript からのメッセージの言語を設定します。これは生成には影響を与えません。", "Set_the_module_option_in_your_configuration_file_to_0_95099": "構成ファイルの 'module' オプションを '{0}' に設定する", "Set_the_newline_character_for_emitting_files_6659": "ファイルを生成するための改行文字を設定します。", "Set_the_target_option_in_your_configuration_file_to_0_95098": "構成ファイルの 'target' オプションを '{0}' に設定する", "Setters_cannot_return_a_value_2408": "セッターは値を返せません。", "Show_all_compiler_options_6169": "コンパイラ オプションをすべて表示します。", "Show_diagnostic_information_6149": "診断情報を表示します。", "Show_verbose_diagnostic_information_6150": "詳細な診断情報を表示します。", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "ビルドされる (または '--clean' で指定される場合は、削除される) 内容を表示する", "Signature_0_must_be_a_type_predicate_1224": "シグネチャ '{0}' は型の述語である必要があります。", "Signature_declarations_can_only_be_used_in_TypeScript_files_8017": "署名宣言は TypeScript ファイルでのみ使用できます。", "Skip_building_downstream_projects_on_error_in_upstream_project_6640": "アップストリーム プロジェクトのエラー時にダウンストリーム プロジェクトのビルドをスキップします。", "Skip_type_checking_all_d_ts_files_6693": "すべての .d.ts ファイルについて型チェックをスキップします。", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "TypeScript に含まれている .d.ts ファイルの型チェックをスキップします。", "Skip_type_checking_of_declaration_files_6012": "宣言ファイルの型チェックをスキップします。", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Skipping build of project '{0}' because its dependency '{1}' has errors", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Skipping build of project '{0}' because its dependency '{1}' was not built", "Skipping_module_0_that_looks_like_an_absolute_URI_target_file_types_Colon_1_6164": "絶対 URI のように見えるモジュール '{0}' をスキップしています。ターゲット ファイルの種類: {1}。", "Source_from_referenced_project_0_included_because_1_specified_1414": "'{1}' が指定されたため、参照先プロジェクト '{0}' からソースがインクルードされました", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "'--module' が 'none' として指定されたため、参照先プロジェクト '{0}' からソースがインクルードされました", "Source_has_0_element_s_but_target_allows_only_1_2619": "ソースには {0} 個の要素がありますが、ターゲットで使用できるのは {1} 個のみです。", "Source_has_0_element_s_but_target_requires_1_2618": "ソースには {0} 個の要素が含まれていますが、ターゲットには {1} 個が必要です。", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "ソースには、ターゲットの位置 {0} にある必須要素と一致するものがありません。", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "ソースには、ターゲットの位置 {0} にある可変個引数要素と一致するものがありません。", "Specify_ECMAScript_target_version_6015": "ECMAScript ターゲット バージョンを指定します。", "Specify_JSX_code_generation_6080": "JSX コードの生成を指定します。", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "1 つの JavaScript ファイルにすべての出力をバンドルするファイルを指定します。'declaration' が true の場合は、すべての .d.ts 出力をバンドルするファイルも指定します。", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "コンパイルに含めるファイルに一致する glob パターンの一覧を指定します。", "Specify_a_list_of_language_service_plugins_to_include_6681": "含める言語サービス プラグインの一覧を指定します。", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "ターゲットのランタイム環境を記述する、バンドルされたライブラリ宣言ファイルのセットを指定します。", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "追加の検索場所にインポートを再マップするエントリのセットを指定します。", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "プロジェクトのパスを指定するオブジェクトの配列を指定します。プロジェクトの参照で使用されます。", "Specify_an_output_folder_for_all_emitted_files_6678": "すべての生成されたファイルに対して出力フォルダーを指定します。", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "型にのみ使用されるインポートの生成または確認動作を指定します。", "Specify_file_to_store_incremental_compilation_information_6380": "増分コンパイル情報を格納するファイルを指定する", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "TypeScript を使用して、指定されたモジュール指定子でファイルを検索する方法を指定します。", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "再帰的なファイル ウォッチ機能を持たないシステムでディレクトリをウォッチするための方法を指定します。", "Specify_how_the_TypeScript_watch_mode_works_6715": "TypeScript ウォッチ モードの動作方法を指定します。", "Specify_library_files_to_be_included_in_the_compilation_6079": "コンパイルに含めるライブラリ ファイルを指定します。", "Specify_module_code_generation_6016": "モジュール コードの生成を指定します。", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "'jsx: react-jsx*' を使用するときに JSX ファクトリ関数のインポートに使用するモジュール指定子を指定します。", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "'./node_modules/@types' のように動作する複数のフォルダーを指定します。", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "設定の継承元となる基本構成ファイルへのパスまたはノード モジュール参照を 1 つまたは複数指定します。", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "宣言ファイルの自動取得に関するオプションを指定します。", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "ファイル システムのイベントを使用して作成できなかった場合に、ポーリング監視を作成する方法を指定します: 'FixedInterval' (既定)、'PriorityInterval'、'DynamicPriority'、'FixedChunkSize'。", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "再帰的な監視をネイティブでサポートしていないプラットフォーム上のディレクトリを監視する方法を指定します: 'UseFsEvents' (既定)、'FixedPollingInterval'、'DynamicPriorityPolling'、'FixedChunkSizePolling'。", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "ファイルの監視方法を指定します: 'FixedPollingInterval' (既定)、'PriorityPollingInterval'、'DynamicPriorityPolling'、'FixedChunkSizePolling'、'UseFsEvents'、'UseFsEventsOnParentDirectory'。", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "React JSX 発行を対象とするときにフラグメントに使用される JSX フラグメント参照を指定します ('React.Fragment' や 'Fragment' など)。", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "'react' JSX 発行 ('React.createElement' や 'h') などを対象とするときに使用する JSX ファクトリ関数を指定します。", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "React JSX 発行を対象とするときに使用される JSX ファクトリ関数を指定します ('React.createElement' や 'h' など)。", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "'jsxFactory' コンパイラ オプションを指定して 'react' JSX 生成をターゲットにするときに使用する JSX フラグメント ファクトリ関数を指定します (例: 'Fragment')。", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "非相対モジュール名を解決するための基本ディレクトリを指定します。", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "ファイルの生成時に使用する行シーケンスの末尾を指定します: 'CRLF' (dos) または 'LF' (unix)。", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "デバッガーがソースの場所の代わりに TypeScript ファイルを検索する必要のある場所を指定します。", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "デバッガーが、生成された場所の代わりにマップ ファイルを検索する必要のある場所を指定します。", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "'node_modules' で JavaScript ファイルを確認するために使用するフォルダーの深さの最大値を指定します。'allowJs' にのみ適用可能です。", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "'jsx' と 'jsxs' のファクトリ関数をインポートするために使用されるモジュール指定子を指定します。例: react", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "'createElement' に対して呼び出されたオブジェクトを指定します。これは、'react' JSX 発行を対象とする場合にのみ適用されます。", "Specify_the_output_directory_for_generated_declaration_files_6613": "生成された宣言ファイルの出力ディレクトリを指定します。", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": ".tsbuildinfo 増分コンパイル ファイルへのパスを指定します。", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "入力ファイルのルート ディレクトリを指定します。--outDir とともに、出力ディレクトリ構造の制御に使用します。", "Specify_the_root_folder_within_your_source_files_6690": "ソース ファイル内のルート フォルダーを指定します。", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "デバッガーのルート パスを指定して、参照ソース コードを検索します。", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "ソース ファイルに参照されずに含める型のパッケージ名を指定します。", "Specify_what_JSX_code_is_generated_6646": "生成済みの JSX コードを指定します。", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "システムがネイティブ ファイル ウォッチャーを使い果たした場合に、ウォッチャーが使用するアプローチを指定します。", "Specify_what_module_code_is_generated_6657": "生成済みのモジュール コードを指定します。", "Split_all_invalid_type_only_imports_1367": "無効な型のみのインポートをすべて分割する", "Split_into_two_separate_import_declarations_1366": "2 つの別個のインポート宣言に分割する", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "'new' 式のスプレッド演算子は ECMAScript 5 以上をターゲットにする場合にのみ使用できます。", "Spread_types_may_only_be_created_from_object_types_2698": "spread 型はオブジェクトの種類からのみ作成できます。", "Starting_compilation_in_watch_mode_6031": "ウォッチ モードでのコンパイルを開始しています...", "Statement_expected_1129": "ステートメントが必要です。", "Statements_are_not_allowed_in_ambient_contexts_1036": "ステートメントは環境コンテキストでは使用できません。", "Static_members_cannot_reference_class_type_parameters_2302": "静的メンバーはクラスの型パラメーターを参照できません。", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "静的プロパティ '{0}' がコンストラクター関数 '{1}' のビルトイン プロパティ 'Function.{0}' と競合しています。", "String_literal_expected_1141": "文字列リテラルが必要です。", "String_literal_import_and_export_names_are_not_supported_when_the_module_flag_is_set_to_es2015_or_es_18057": "'--module' フラグが 'es2015' または 'es2020' に設定されている場合、文字列リテラルのインポートおよびエクスポート名はサポートされません。", "String_literal_with_double_quotes_expected_1327": "二重引用符を含む文字列リテラルが必要です。", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "色とコンテキストを使用してエラーとメッセージにスタイルを適用します (試験的)。", "Subpattern_flags_must_be_present_when_there_is_a_minus_sign_1504": "マイナス記号がある場合は、サブパターン フラグが存在する必要があります。", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "後続のプロパティ宣言は同じ型でなければなりません。プロパティ '{0}' の型は '{1}' である必要がありますが、ここでは型が '{2}' になっています。", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "後続の変数宣言は同じ型でなければなりません。変数 '{0}' の型は '{1}' である必要がありますが、'{2}' になっています。", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "パターン '{1}' の代入 '{0}' の型が正しくありません。必要な型は 'string' ですが、'{2}' を取得しました。", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "パターン '{1}' の置換 '{0}' に使用できる '*' 文字は 1 文字だけです。", "Substitutions_for_pattern_0_should_be_an_array_5063": "パターン '{0}' への代入は配列でなければなりません。", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "パターン '{0}' への代入を空の配列にすることはできません。", "Successfully_created_a_tsconfig_json_file_6071": "tsconfig.json ファイルが正常に作成されました。", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "super の呼び出しは、コンストラクターの外部、またはコンストラクター内の入れ子になった関数では使用できません。", "Suppress_excess_property_checks_for_object_literals_6072": "オブジェクト リテラルの過剰なプロパティ確認を抑制します。", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "インデックス シグニチャのないオブジェクトにインデックスを作成するため、noImplicitAny エラーを抑制します。", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "インデックス シグネチャのないオブジェクトにインデックスを作成する際、'noImplicitAny' エラーを表示しません。", "Switch_each_misused_0_to_1_95138": "誤用されている各 '{0}' を '{1}' に切り替えてください", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "再帰的なウォッチをネイティブでサポートしていないプラットフォーム上で、同期的にコールバックを呼び出してディレクトリ ウォッチャーの状態を更新します。", "Syntax_Colon_0_6023": "構文: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "タグ '{0}' には少なくとも '{1}' 個の引数が必要ですが、JSX ファクトリ '{2}' で提供されるのは最大 '{3}' 個です。", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "タグ付きテンプレート式は、省略可能なチェーンでは許可されていません。", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "ターゲットには {0} 個の要素のみを使用できますが、ソースにはそれより多くを指定できます。", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "ターゲットには {0} 個の要素が必要ですが、ソースに指定する数はそれより少なくても構いません。", "Target_signature_provides_too_few_arguments_Expected_0_or_more_but_got_1_2849": "ターゲット署名の引数が少なすぎます。{0} 以上が必要ですが、{1} でした。", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "'{0}' 修飾子は TypeScript ファイルでのみ使用できます。", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "'{0}' 演算子を 'symbol' 型に適用することはできません。", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "'{0}' 演算子はブール型には使用できません。代わりに '{1}' を使用してください。", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "非同期反復子の '{0}' プロパティはメソッドである必要があります。", "The_0_property_of_an_iterator_must_be_a_method_2767": "反復子の '{0}' プロパティはメソッドである必要があります。", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "'Object' 型を割り当てることができるその他の型はごく少数です。代わりの候補には 'any' 型があります。", "The_Unicode_u_flag_and_the_Unicode_Sets_v_flag_cannot_be_set_simultaneously_1502": "Unicode (u) フラグと Unicode Sets (v) フラグを同時に設定することはできません。", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES5_Consider_using_a_standard_func_2496": "'arguments' オブジェクトは、ES5 のアロー関数で参照することはできません。標準の関数式の使用を考慮してください。", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES5_Consider_using_a_sta_2522": "'arguments' オブジェクトは、ES5 の非同期関数またはメソッドで参照することはできません。標準の関数またはメソッドを使用することを検討してください。", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "'if' ステートメントの本文を空のステートメントにすることはできません。", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "呼び出しはこの実装に対して成功した可能性がありますが、オーバーロードの実装シグネチャは外部からは参照できません。", "The_character_set_of_the_input_files_6163": "入力ファイルの文字セット。", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "含まれているアロー関数は、'this' のグローバル値をキャプチャします。", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "含まれている関数またはモジュールの本体は、制御フロー解析には大きすぎます。", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "現在のファイルは CommonJS モジュールであり、最上位レベルでは 'await' を使用できません。", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "現在のファイルは CommonJS モジュールであり、このインポートでは 'require' 呼び出しが生成されますが、参照ファイルは ECMAScript モジュールであるため、'require' ではインポートできません。代わりに動的な 'import(\"{0}\")' 呼び出しを記述することを検討してください。", "The_current_host_does_not_support_the_0_option_5001": "現在のホストは '{0}' オプションをサポートしていません。", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "使用するつもりだったと思われる '{0}' の宣言はここで定義されています", "The_declaration_was_marked_as_deprecated_here_2798": "この宣言はここで非推奨とマークされました。", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "予期された型は、型 '{1}' に対してここで宣言されたプロパティ '{0}' から取得されています", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "予期された型は、このシグネチャの戻り値の型に基づいています。", "The_expected_type_comes_from_this_index_signature_6501": "予期された型は、このインデックス シグネチャに基づいています。", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "エクスポートの代入の式は、環境コンテキストの識別子または修飾名にする必要があります。", "The_file_is_in_the_program_because_Colon_1430": "ファイルがプログラム内に存在します。理由:", "The_files_list_in_config_file_0_is_empty_18002": "構成ファイル '{0}' の 'files' リストが空です。", "The_first_export_default_is_here_2752": "最初のエクスポートの既定値はここにあります。", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "Promise では、'then' メソッドの最初のパラメーターはコールバックでなければなりません。", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "グローバル型 'JSX.{0}' には複数のプロパティが含まれていない可能性があります。", "The_implementation_signature_is_declared_here_2750": "実装シグネチャはここで宣言されています。", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "'import.meta' メタプロパティは、CommonJS 出力にビルドするファイルでは許可されていません。", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "'import.meta' メタプロパティは、'--module' オプションが 'es2020'、'es2022'、'esnext'、'system'、'node16'、'node18'、または 'nodenext' である場合にのみ許可されます。", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "'{0}' の推論された型には、'{1}' への参照なしで名前を付けることはできません。これは、移植性がない可能性があります。型の注釈が必要です。", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "'{0}' の推論された型は、循環構造を持つ型を参照しています。この型のシリアル化は自明ではありません。型の注釈が必要です。", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "'{0}' の推定型はアクセス不可能な '{1}' 型を参照します。型の注釈が必要です。", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "このノードの推定型は、コンパイラがシリアル化する最大長を超えています。明示的な型の注釈が必要です。", "The_initializer_of_a_using_declaration_must_be_either_an_object_with_a_Symbol_dispose_method_or_be_n_2850": "'using' 宣言の初期化子は、'[Symbol.dispose]()' メソッドを持つオブジェクトであるか、'null' または 'undefined' である必要があります。", "The_initializer_of_an_await_using_declaration_must_be_either_an_object_with_a_Symbol_asyncDispose_or_2851": "'await using' 宣言の初期化子は、'[Symbol.asyncDispose]()' または '[Symbol.dispose]5D;()' メソッドを持つオブジェクトであるか、'null' または 'undefined' である必要があります。", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "交差 '{0}' は 'なし' に縮小されました。プロパティ '{1}' が複数の構成要素に存在し、一部ではプライベートであるためです。", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "交差 '{0}' は 'なし' に縮小されました。一部の構成要素でプロパティ '{1}' の型が競合しているためです。", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "'組み込み' キーワードは、コンパイラが提供する組み込み型を宣言する場合にのみ使用できます。", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "'jsxFactory' コンパイラ オプションで JSX フラグメントを使用するには、'jsxFragmentFactory' コンパイラ オプションを指定する必要があります。", "The_last_overload_gave_the_following_error_2770": "前回のオーバーロードにより、次のエラーが発生しました。", "The_last_overload_is_declared_here_2771": "前回のオーバーロードはここで宣言されています。", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "'for...in' ステートメントの左側を非構造化パターンにすることはできません。", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_using_declaration_1493": "'for...in' ステートメントの左側を 'using' 宣言にすることはできません。", "The_left_hand_side_of_a_for_in_statement_cannot_be_an_await_using_declaration_1494": "'for...in' ステートメントの左側を 'await using' 宣言にすることはできません。", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "'for...in' ステートメントの左側で型の注釈を使用することはできません。", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "'for...in' ステートメントの左辺には、省略可能なプロパティ アクセスを指定できません。", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "'for...in' ステートメントの左側は、変数またはプロパティ アクセスである必要があります。", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "'for...in' ステートメントの左側の型は 'string' または 'any' でなければなりません。", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "'for...of' ステートメントの左側で型の注釈を使用することはできません。", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "'for...of' ステートメントの左辺には、省略可能なプロパティ アクセスを指定できません。", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "'for...of' ステートメントの左辺には、'async' を指定できません。", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "'for...of' ステートメントの左側は、変数またはプロパティ アクセスである必要があります。", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "算術演算の左辺には、'any' 型、'number' 型、'bigint' 型、または列挙型を指定する必要があります。", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "代入式の左辺には、省略可能なプロパティ アクセスを指定できません。", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "代入式の左側は、変数またはプロパティ アクセスである必要があります。", "The_left_hand_side_of_an_instanceof_expression_must_be_assignable_to_the_first_argument_of_the_right_2860": "'instanceof' 式の左側は、右側の '[Symbol.hasInstance]' メソッドの最初の引数に割り当て可能である必要があります。", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "'instanceof' 式の左辺には、'any' 型、オブジェクト型、または型パラメーターを指定してください。", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "ユーザーにメッセージを表示するときに使用するロケール (例: 'en-us')", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "node_modules の下を検索して JavaScript ファイルを読み込む依存関係の最大深度です。", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "'delete' 演算子のオペランドには、private 識別子を指定できません。", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "'delete' 演算子のオペランドには、読み取り専用のプロパティを指定できません。", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "'delete' 演算子のオペランドはプロパティ参照である必要があります。", "The_operand_of_a_delete_operator_must_be_optional_2790": "'delete' 演算子のオペランドはオプションである必要があります。", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "インクリメント演算子またはデクリメント演算子のオペランドには、省略可能なプロパティ アクセスを指定することはできません。", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "インクリメントまたはデクリメント演算子のオペランドは、変数またはプロパティ アクセスである必要があります。", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "パーサーは、ここで '{0}' トークンに一致する '{1}' を予期していました。", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "プロジェクト ルートはあいまいですが、ファイル '{1}' のエクスポート マップ エントリ '{0}' を解決するために必要です。あいまいさを解消するには、'rootDir' コンパイラ オプションを指定します。", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "プロジェクト ルートはあいまいですが、ファイル '{1}' のインポート マップ エントリ '{0}' を解決するために必要です。あいまいさを解消するには、'rootDir' コンパイラ オプションを指定します。", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "同じスペルの別の private 識別子によってシャドウされているため、このクラス内の型 '{1}' ではプロパティ '{0}' にアクセスできません。", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "パラメーター デコレーター関数の戻り値の型は、'void' か 'any' である必要があります。", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "プロパティ デコレーター関数の戻り値の型は、'void' か 'any' である必要があります。", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "非同期関数の戻り値の型は、有効な Promise であるか、呼び出し可能な 'then' メンバーを含んでいないかのどちらかであることが必要です。", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1065": "非同期関数または非同期メソッドの戻り値の型は、グローバル Promise<T> 型である必要があります。", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "非同期関数または非同期メソッドの戻り値の型は、グローバル Promise<T> 型である必要があります。'Promise<{0}>' と書き込むつもりでしたか?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "'for...in' ステートメントの右側には、'any' 型、オブジェクト型、型パラメーターを指定する必要がありますが、ここでは型 '{0}' が指定されています。", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "算術演算の右辺には、'any' 型、'number' 型、'bigint' 型、または列挙型を指定する必要があります。", "The_right_hand_side_of_an_instanceof_expression_must_be_either_of_type_any_a_class_function_or_other_2359": "'instanceof' 式の右側には、型 'any'、クラス、関数、または 'Function' インターフェイス型に割り当て可能なその他の型、または 'Symbol.hasInstance' メソッドを持つオブジェクト型のいずれかを指定する必要があります。", "The_right_hand_side_of_an_instanceof_expression_must_not_be_an_instantiation_expression_2848": "'instanceof' 式の右側をインスタンス化式にすることはできません。", "The_root_value_of_a_0_file_must_be_an_object_5092": "'{0}' ファイルのルート値はオブジェクトである必要があります。", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_0_1278": "このランタイムは {1} 引数を指定してデコレーターを呼び出しますが、デコレーターには {0} が必要です。", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_at_least_0_1279": "このランタイムは {1} 引数を指定してデコレーターを呼び出しますが、デコレーターには少なくとも {0} が必要です。", "The_shadowing_declaration_of_0_is_defined_here_18017": "'{0}' のシャドウ宣言はここで定義されています", "The_signature_0_of_1_is_deprecated_6387": "'{1}' のシグネチャ '{0}' は非推奨です。", "The_specified_path_does_not_exist_Colon_0_5058": "指定されたパスがありません: '{0}'。", "The_tag_was_first_specified_here_8034": "このタグは最初にこちらで指定されました。", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "オブジェクト rest の代入先を、省略可能なプロパティ アクセスにすることはできません。", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "オブジェクトの残り部分の代入の対象は、変数またはプロパティ アクセスである必要があります。", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "型 '{0}' の 'this' コンテキストを型 '{1}' のメソッドの 'this' に割り当てることはできません。", "The_this_types_of_each_signature_are_incompatible_2685": "各シグネチャの 'this' 型に互換性がありません。", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "型 '{0}' は 'readonly' であるため、変更可能な型 '{1}' に代入することはできません。", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "名前付きエクスポートで、export ステートメントに 'export type' が使用されている場合、'type' 修飾子は使用できません。", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "名前付きインポートで、import ステートメントに 'import type' が使用されている場合、'type' 修飾子は使用できません。", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "関数の宣言の型は、関数のシグネチャと一致する必要があります。", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "このノードの種類は、そのプロパティ '{0}' をシリアル化できないため、シリアル化できません。", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "非同期反復子の '{0}()' メソッドによって返される型は、'value' プロパティを持つ型に対する promise である必要があります。", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "反復子の '{0}()' メソッドによって返される型には 'value' プロパティが必要です。", "The_types_of_0_are_incompatible_between_these_types_2200": "'{0}' の型は、これらの型同士で互換性がありません。", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "'{0}' によって返された型は、これらの型同士で互換性がありません。", "The_value_0_cannot_be_used_here_18050": "値 '{0}' はここでは使用できません。", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "'for...in' ステートメントの変数宣言に初期化子を指定することはできません。", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "'for...of' ステートメントの変数宣言に初期化子を指定することはできません。", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "'with' ステートメントはサポートされていません。'with' ブロック内のすべてのシンボルの型は 'any' になります。", "There_are_types_at_0_but_this_result_could_not_be_resolved_under_your_current_moduleResolution_setti_6280": "'{0}' に型がありますが、現在の 'moduleResolution' 設定ではこの結果を解決できませんでした。'node16'、'nodenext'、または 'bundler' への更新を検討してください。", "There_are_types_at_0_but_this_result_could_not_be_resolved_when_respecting_package_json_exports_The__6278": "'{0}' に型がありますが、package.json \"exports\" を尊重しながらこの結果を解決できませんでした。'{1}' ライブラリの package.json または入力を更新する必要がある場合があります。", "There_is_no_capturing_group_named_0_in_this_regular_expression_1532": "この正規表現には '{0}' という名前のキャプチャ グループはありません。", "There_is_nothing_available_for_repetition_1507": "繰り返しに使用できるものがありません。", "This_JSX_tag_requires_0_to_be_in_scope_but_it_could_not_be_found_2874": "この JSX タグでは '{0}' がスコープ内に存在する必要がありますが、見つかりませんでした。", "This_JSX_tag_requires_the_module_path_0_to_exist_but_none_could_be_found_Make_sure_you_have_types_fo_2875": "この JSX タグにはモジュール パス '{0}' が存在する必要がありますが、見つかりませんでした。適切なパッケージの種類がインストールされていることを確認してください。", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "この JSX タグの '{0}' prop は型 '{1}' の単一の子を予期しますが、複数の子が指定されました。", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "この JSX タグの '{0}' prop は複数の子を必要とする型 '{1}' を予期しますが、単一の子が指定されました。", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_no_capturing_groups_in_this_regul_1534": "この前方参照は、存在しないグループを参照しています。この正規表現にはキャプチャ グループがありません。", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_only_0_capturing_groups_in_this_r_1533": "この前方参照は、存在しないグループを参照しています。この正規表現には {0} キャプチャ グループのみがあります。", "This_binary_expression_is_never_nullish_Are_you_missing_parentheses_2870": "このバイナリ式が NULL 値になることはありません。かっこを忘れていませんか?", "This_character_cannot_be_escaped_in_a_regular_expression_1535": "この文字を正規表現内でエスケープすることはできません。", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "'{0}' 型と '{1}' 型が重複していないため、この比較は意図したとおりに表示されない可能性があります。", "This_condition_will_always_return_0_2845": "この条件は常に '{0}' を返します。", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "JavaScript が値ではなく参照でオブジェクトを比較するため、この条件は常に '{0}' を返します。", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "'{0}' が常に定義されているため、この条件は常に true を返します。", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "この関数は常に定義されているため、この条件は常に true を返します。代わりにこれを呼び出すことを意図していましたか?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "このコンストラクター関数はクラス宣言に変換される可能性があります。", "This_expression_is_always_nullish_2871": "この式は常に null です。", "This_expression_is_not_callable_2349": "この式は呼び出し可能ではありません。", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "この式は 'get' アクセサーであるため、呼び出すことができません。'()' なしで使用しますか?", "This_expression_is_not_constructable_2351": "この式はコンストラクト可能ではありません。", "This_file_already_has_a_default_export_95130": "このファイルには、既に既定のエクスポートがあります", "This_import_path_is_unsafe_to_rewrite_because_it_resolves_to_another_project_and_the_relative_path_b_2878": "このインポート パスは別のプロジェクトに解決され、プロジェクトの出力ファイル間の相対パスが入力ファイル間の相対パスと同じではないため、書き換えは安全ではありません。", "This_import_uses_a_0_extension_to_resolve_to_an_input_TypeScript_file_but_will_not_be_rewritten_duri_2877": "このインポートでは、'{0}' 拡張子を使用して入力 TypeScript ファイルに解決されますが、生成中に書き換えられるのは相対パスではないためです。", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "これは拡張される宣言です。拡張する側の宣言を同じファイルに移動することを検討してください。", "This_kind_of_expression_is_always_falsy_2873": "この種の式は常に false です。", "This_kind_of_expression_is_always_truthy_2872": "この種の式は常に true です。", "This_may_be_converted_to_an_async_function_80006": "これは非同期関数に変換できます。", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "このメンバーは、基底クラス '{0}' で宣言されていないため、このメンバーに '@override' タグを含む JSDoc コメントを指定することはできません。", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "このメンバーは、基底クラス '{0}' で宣言されていないため、このメンバーに 'override' タグを含む JSDoc コメントを指定することはできません。'{1}' ということですか?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "このメンバーを含んでいるクラス '{0}' が別のクラスを拡張していないため、このメンバーに '@override' タグを含む JSDoc コメントを指定することはできません。", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_name_is_dynamic_4128": "このメンバーの名前は動的であるため、'@override' タグが含まれた JSDoc コメントを保持することはできません。", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "このメンバーは、基底クラス '{0}' で宣言されていないため、'override' 修飾子を指定することはできません。", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "このメンバーは、基底クラス '{0}' で宣言されていないため、'override' 修飾子を指定することはできません。'{1}' ということですか?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "このメンバーを含んでいるクラス '{0}' が別のクラスを拡張していないため、このメンバーに 'override' 修飾子を指定することはできません。", "This_member_cannot_have_an_override_modifier_because_its_name_is_dynamic_4127": "名前が動的であるため、このメンバーに 'override' 修飾子を指定することはできません。", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "このメンバーには、基底クラス '{0}' のメンバーをオーバーライドするため、'@override' タグを含む JSDoc コメントが必要です。", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "このメンバーは、基底クラス '{0}' のメンバーをオーバーライドするため、'override' 修飾子を指定する必要があります。", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "このメンバーは、基底クラス '{0}' で宣言された抽象メソッドをオーバーライドするため、'override' 修飾子を指定する必要があります。", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "このモジュールは、'{0}' フラグをオンにして既定のエクスポートを参照することにより、ECMAScript のインポートまたはエクスポートのみを使用して参照できます。", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "このモジュールは、'export =' を使用して宣言されており、'{0}' フラグを使用する場合は既定のインポートでのみ使用できます。", "This_operation_can_be_simplified_This_shift_is_identical_to_0_1_2_6807": "この操作は簡略化できます。このシフトは '{0} {1} {2}' と同じです。", "This_overload_implicitly_returns_the_type_0_because_it_lacks_a_return_type_annotation_7012": "このオーバーロードは、戻り値の型の注釈がないため、型 '{0}' を暗黙的に返します。", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "このオーバーロード シグネチャには、実装シグネチャとの互換性はありません。", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "このパラメーターは、'use strict' ディレクティブと共に使用することはできません。", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "このパラメーター プロパティには、基底クラス '{0}' のメンバーをオーバーライドするため、'@override' タグを含む JSDoc コメントが必要です。", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "このメンバーは、基底クラス '{0}' のメンバーをオーバーライドするため、パラメーター プロパティに 'override' 修飾子がある必要があります。", "This_regular_expression_flag_cannot_be_toggled_within_a_subpattern_1509": "この正規表現フラグをサブパターン内で切り替えることはできません。", "This_regular_expression_flag_is_only_available_when_targeting_0_or_later_1501": "この正規表現フラグは、'{0}' 以降をターゲットにする場合にのみ使用できます。", "This_relative_import_path_is_unsafe_to_rewrite_because_it_looks_like_a_file_name_but_actually_resolv_2876": "この相対インポート パスは、ファイル名のようですが、実際には \"{0}\" に解決されるため、書き換えは安全ではありません。", "This_spread_always_overwrites_this_property_2785": "このスプレッドは、常にこのプロパティを上書きします。", "This_syntax_is_not_allowed_when_erasableSyntaxOnly_is_enabled_1294": "'erasableSyntaxOnly' が有効な場合、この構文は使用できません。", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "この構文は、拡張子が .mts または .cts のファイルで予約されています。末尾のコンマまたは明示的な制約を追加します。", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "この構文は、拡張子が .mts または .cts のファイルで予約されています。代わりに `as` 式を使用してください。", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "この構文にはインポートされたヘルパーが必要ですが、モジュール '{0}' が見つかりません。", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "この構文には、'{1}' という名前のインポートされたヘルパーが必要ですが、'{0}' には存在しません。'{0}' のバージョンのアップグレードを検討してください。", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "この構文には、{2} パラメーターを持つ '{1}' という名前のインポートされたヘルパーが必要ですが、'{0}' にあるものと互換性がありません。'{0}' のバージョンをアップグレードすることをご検討ください。", "This_type_parameter_might_need_an_extends_0_constraint_2208": "この型パラメーターには 'extends {0}' 制約が必要な場合があります。", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "この 'import' の使用は無効です。'import()' 呼び出しは書き込むことができますが、かっこが必要であり、型引数を指定することはできません。", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "このファイルを ECMAScript モジュールに変換するには、フィールド '\"type\": \"module\"' を '{0}' に追加します。", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "このファイルを ECMAScript モジュールに変換するには、ファイル拡張子を '{0}' に変更するか、フィールド '\"type\": \"module\"' を '{1}' に追加します。", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "このファイルを ECMAScript モジュールに変換するには、ファイル拡張子を '{0}' に変更するか、'{ \"type\": \"module\" }' を含むローカルの package.json ファイルを作成します。", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "このファイルを ECMAScript モジュールに変換するには、'{ \"type\": \"module\" }' を含むローカルの package.json ファイルを作成します。", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "トップレベルの 'await' 式は、'module' オプションが 'es2022'、'esnext'、'system'、'node16'、'node18'、'nodenext'、または 'preserve' に設定されていて、'target' オプションが 'es2017' 以上に設定されている場合にのみ許可されています。", "Top_level_await_using_statements_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_sys_2854": "トップレベルの 'await using' ステートメントは、'module' オプションが 'es2022'、'esnext'、'system'、'node16'、'node18'、'nodenext'、または 'preserve' に設定されていて、'target' オプションが 'es2017' 以上に設定されている場合にのみ許可されています。", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": ".d.ts ファイルのトップレベルの宣言は、'declare' または 'export' 修飾子で始める必要があります。", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "トップレベルの 'for await' ループは、'module' オプションが 'es2022'、'esnext'、'system'、'node16'、'node18'、'nodenext'、または 'preserve' に設定されていて、'target' オプションが 'es2017' 以上に設定されている場合にのみ許可されています。", "Trailing_comma_not_allowed_1009": "末尾にコンマは使用できません。", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "個々のモジュールとして各ファイルをトランスパイルします ('ts.transpileModule' に類似)。", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "存在する場合は `npm i --save-dev @types/{1}` を試すか、`declare module '{0}';` を含む新しい宣言 (.d.ts) ファイルを追加します", "Trying_other_entries_in_rootDirs_6110": "'rootDirs' の他のエントリを試しています。", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "代入 '{0}' を試しています。候補のモジュールの場所: '{1}'。", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "長さ '{1}' のタプル型 '{0}' にインデックス '{2}' の要素がありません。", "Tuple_type_arguments_circularly_reference_themselves_4110": "タプル型の引数は、それ自体を循環参照します。", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "'{0}' の種類は、'--downlevelIteration' フラグを使用している場合、または 'es2015' 以降の '--target' を使用している場合にのみ反復処理できます。", "Type_0_cannot_be_used_as_an_index_type_2538": "型 '{0}' はインデックスの型として使用できません。", "Type_0_cannot_be_used_to_index_type_1_2536": "型 '{0}' はインデックスの種類 '{1}' に使用できません。", "Type_0_does_not_satisfy_the_constraint_1_2344": "型 '{0}' は制約 '{1}' を満たしていません。", "Type_0_does_not_satisfy_the_expected_type_1_1360": "型 '{0}' は想定された型 '{1}' を満たしていません。", "Type_0_has_no_call_signatures_2757": "型 '{0}' には呼び出しシグネチャがありません。", "Type_0_has_no_construct_signatures_2761": "型 '{0}' にはコンストラクト シグネチャがありません。", "Type_0_has_no_matching_index_signature_for_type_1_2537": "型 '{0}' には型 '{1}' と一致するインデックス シグネチャがありません。", "Type_0_has_no_properties_in_common_with_type_1_2559": "型 '{0}' には型 '{1}' と共通のプロパティがありません。", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "型 '{0}' には、型引数リストを適用できるシグネチャがありません。", "Type_0_is_generic_and_can_only_be_indexed_for_reading_2862": "型 '{0}' は汎用であり、読み取り専用にしかインデックスを作成できません。", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "型 '{0}' には 型 '{1}' からの次のプロパティがありません: {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "型 '{0}' には 型 '{1}' からの次のプロパティがありません: {2}、{3} など。", "Type_0_is_not_a_constructor_function_type_2507": "型 '{0}' はコンストラクター関数型ではありません。", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compa_1055": "型 '{0}' は Promise と互換性のあるコンストラクター値を参照しないため、ES5 において有効な非同期関数の戻り値の型ではありません。", "Type_0_is_not_an_array_type_2461": "型 '{0}' は配列型ではありません。", "Type_0_is_not_an_array_type_or_a_string_type_2495": "型 '{0}' は配列型でも文字列型でもありません。", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "型 '{0}' は、配列型でも文字列型でもないか、反復子を返す '[Symbol.iterator]()' メソッドを持っていません。", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "型 '{0}' は、配列型ではないか、反復子を返す '[Symbol.iterator]()' メソッドを持っていません。", "Type_0_is_not_assignable_to_type_1_2322": "型 '{0}' を型 '{1}' に割り当てることはできません。", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "型 '{0}' を型 '{1}' に割り当てることはできません。'{2}' でよろしいですか?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "型 '{0}' は型 '{1}' に割り当てられません。同じ名前で 2 つの異なる型が存在しますが、これは関連していません。", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "型 '{0}' は、差異注釈によって暗黙的に示されているように、型 '{1}' に割り当てできません。", "Type_0_is_not_assignable_to_type_1_as_required_for_computed_enum_member_values_18033": "型 '{0}' は、計算された列挙型メンバーの要求どおりに型 '{1}' に割り当てることはできません。", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "型 '{0}' を、'exactOptionalPropertyTypes: true' が指定されている型 '{1}' に割り当てることはできません。ターゲットのプロパティの型に 'undefined' を追加することを検討してください。", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "型 '{0}' を、'exactOptionalPropertyTypes: true' が指定されている型 '{1}' に割り当てることはできません。ターゲットの型に 'undefined' を追加することを検討してください。", "Type_0_is_not_comparable_to_type_1_2678": "型 '{0}' は型 '{1}' と比較できません。", "Type_0_is_not_generic_2315": "型 '{0}' はジェネリックではありません。", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "型 '{0}' は、'in' 演算子の右オペランドとして許可されていないプリミティブ値を表す場合があります。", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "型 '{0}' には、非同期反復子を返す '[Symbol.asyncIterator]()' メソッドが必要です。", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "型 '{0}' には、反復子を返す '[Symbol.iterator]()' メソッドが必要です。", "Type_0_provides_no_match_for_the_signature_1_2658": "型 '{0}' にはシグネチャ '{1}' に一致するものがありません。", "Type_0_recursively_references_itself_as_a_base_type_2310": "型 '{0}' が、基本型としてそれ自体を再帰的に参照しています。", "Type_Checking_6248": "種類を確認中", "Type_alias_0_circularly_references_itself_2456": "型のエイリアス '{0}' が自身を循環参照しています。", "Type_alias_must_be_given_a_name_1439": "型のエイリアスには名前を指定する必要があります。", "Type_alias_name_cannot_be_0_2457": "型のエイリアス名を '{0}' にすることはできません。", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "型のエイリアスは、TypeScript ファイルでのみ使用できます。", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "型の注釈はコンストラクター宣言では使用できません。", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "型の注釈は、TypeScript ファイルでのみ使用できます。", "Type_argument_expected_1140": "型引数が必要です。", "Type_argument_list_cannot_be_empty_1099": "型引数リストを空にすることはできません。", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "型引数は TypeScript ファイルでのみ使用できます。", "Type_arguments_for_0_circularly_reference_themselves_4109": "'{0}' の型引数はそれ自体を循環参照します。", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "型アサーション式は TypeScript ファイルでのみ使用できます。", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "ソースの位置 {0} にある型は、ターゲットの位置 {1} にある型と互換性がありません。", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "ソースの位置 {0} から {1} にある型は、ターゲットの位置 {2} にある型と互換性がありません。", "Type_containing_private_name_0_can_t_be_used_with_isolatedDeclarations_9039": "プライベート名 '{0}' を含む型は、--isolatedDeclarations と一緒には使用できません。", "Type_declaration_files_to_be_included_in_compilation_6124": "コンパイルに含む型宣言ファイル。", "Type_expected_1110": "型が必要です。", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "型インポート アサーションには、キー `resolution-mode` が 1 つだけ必要です。値は `import` または `require` です。", "Type_import_attributes_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1464": "型インポート属性には、キー 'resolution-mode' が 1 つだけ必要です。値は 'import' または 'require' です。", "Type_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribute_1542": "CommonJS モジュールからの ECMAScript モジュールの型のインポートには、'resolution-mode' 属性が必要です。", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "型のインスタンス化は非常に深く、無限である可能性があります。", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "型は、それ自身の 'then' メソッドのフルフィルメント コールバック内で直接または間接的に参照されます。", "Type_library_referenced_via_0_from_file_1_1402": "ファイル '{1}' から '{0}' を介して参照されたタイプ ライブラリ", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "packageId が '{2}' のファイル '{1}' から '{0}' を介して参照されたタイプ ライブラリ", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "'await' オペランドの型は、有効な Promise であるか、呼び出し可能な 'then' メンバーを含んでいないかのどちらかであることが必要です。", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "計算されたプロパティの値の型は '{0}' です。これは、型 '{1}' に代入することはできません。", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "インスタンス メンバー変数 '{0}' の型は、コンストラクターで宣言された識別子 '{1}' を参照できません。", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "'yield*' オペランドの反復要素の型は、有効な Promise であるか、呼び出し可能な 'then' メンバーを含んでいないかのどちらかであることが必要です。", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "マップされた型 '{1}' で、プロパティ '{0}' の型によってそれ自体が循環参照されています。", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "非同期ジェネレーター内の 'yield' オペランドの型は、有効な Promise であるか、呼び出し可能な 'then' メンバーを含んでいないかのどちらかであることが必要です。", "Type_only_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribut_1541": "CommonJS モジュールからの ECMAScript モジュールの型のみのインポートには、'resolution-mode' 属性が必要です。", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "型はこのインポートで生成されます。名前空間スタイルのインポートは、呼び出すこともコンストラクトすることもできず、実行時にエラーが発生します。代わりに、ここで既定のインポートまたはインポートの require を使用することを検討してください。", "Type_parameter_0_has_a_circular_constraint_2313": "型パラメーター '{0}' に循環制約があります。", "Type_parameter_0_has_a_circular_default_2716": "型パラメーター '{0}' に循環既定値があります。", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "エクスポートされたインターフェイスの呼び出しシグネチャの型パラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "エクスポートされたインターフェイスのコンストラクター シグネチャの型パラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "エクスポートされたクラスの型パラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "エクスポートされた関数の型パラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "エクスポートされたインターフェイスの型パラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "エクスポートされたマップ済みのオブジェクト型の型パラメーター '{0}' が、プライベート名 '{1}' を使用しています。", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "エクスポートした型のエイリアスの型パラメーター '{0}' にプライベート名 '{1}' が指定されているか、これを使用しています。", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "エクスポートされたインターフェイスのメソッドの型パラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "エクスポートされたクラスのパブリック メソッドの型パラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "エクスポートされたクラスのパブリック静的メソッドの型パラメーター '{0}' が、プライベート名 '{1}' を持っているか、使用しています。", "Type_parameter_declaration_expected_1139": "型パラメーターの宣言が必要です。", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "型パラメーターの宣言は TypeScript ファイルでのみ使用できます。", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "型パラメーターの既定値は、以前に宣言された型パラメーターのみを参照できます。", "Type_parameter_list_cannot_be_empty_1098": "型パラメーター リストを空にすることはできません。", "Type_parameter_name_cannot_be_0_2368": "型パラメーター名を '{0}' にすることはできません。", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "型パラメーターはコンストラクター宣言では使用できません。", "Type_predicate_0_is_not_assignable_to_1_1226": "型の述語 '{0}' を '{1}' に割り当てることはできません。", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "型では大きすぎて表すことができないタプル型を生成します。", "Type_reference_directive_0_was_not_resolved_6120": "======== 型参照ディレクティブ '{0}' が解決されませんでした。========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== 型参照ディレクティブ '{0}' が正常に '{1}' に解決されました。プライマリ: {2}。========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== 型参照ディレクティブ '{0}' が正常に '{1}' に解決されました (パッケージ ID '{2}'、プライマリ: {3})。========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "型充足式は TypeScript ファイルでのみ使用できます。", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "JavaScript ファイルのエクスポート宣言に型を含めることはできません。", "Types_have_separate_declarations_of_a_private_property_0_2442": "複数の型に、プライベート プロパティ '{0}' の異なる宣言が含まれています。", "Types_of_construct_signatures_are_incompatible_2419": "コンストラクト シグネチャの型に互換性がありません。", "Types_of_parameters_0_and_1_are_incompatible_2328": "パラメーター '{0}' および '{1}' は型に互換性がありません。", "Types_of_property_0_are_incompatible_2326": "プロパティ '{0}' の型に互換性がありません。", "Unable_to_open_file_0_6050": "ファイル '{0}' を開くことができません。", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "式として呼び出される場合、クラス デコレーターのシグネチャを解決できません。", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "式として呼び出される場合、メソッド デコレーターのシグネチャを解決できません。", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "式として呼び出される場合、パラメーター デコレーターのシグネチャを解決できません。", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "式として呼び出される場合、プロパティ デコレーターのシグネチャを解決できません。", "Undetermined_character_escape_1513": "文字エスケープが不明です。", "Unexpected_0_Did_you_mean_to_escape_it_with_backslash_1508": "予期しない '{0}' です。バックスラッシュを使用してエスケープするつもりでしたか?", "Unexpected_end_of_text_1126": "予期しないテキストの末尾です。", "Unexpected_keyword_or_identifier_1434": "予期しないキーワードまたは識別子です。", "Unexpected_token_1012": "予期しないトークンです。", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "予期しないトークンです。コンストラクター、メソッド、アクセサー、またはプロパティが必要です。", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "予期しないトークンです。型パラメーター名には、中かっこを含めることはできません。", "Unexpected_token_Did_you_mean_or_gt_1382": "予期しないトークンです。`{'>'}` または `&gt;` を意図していましたか?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "予期しないトークンです。`{'}'}` または `&rbrace;` を意図していましたか?", "Unexpected_token_expected_1179": "予期しないトークンです。'{' が必要です。", "Unicode_escape_sequence_cannot_appear_here_17021": "Unicode エスケープ シーケンスはここでは使用できません。", "Unicode_escape_sequences_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v_flag_is_se_1538": "Unicode エスケープ シーケンスは、Unicode (u) フラグまたは Unicode Sets (v) フラグが設定されている場合にのみ使用できます。", "Unicode_property_value_expressions_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v__1530": "Unicode プロパティ値式は、Unicode (u) フラグまたは Unicode Sets (v) フラグが設定されている場合にのみ使用できます。", "Unknown_Unicode_property_name_1524": "Unicode プロパティ名が不明です。", "Unknown_Unicode_property_name_or_value_1529": "Unicode プロパティ名または値が不明です。", "Unknown_Unicode_property_value_1526": "Unicode プロパティ値が不明です。", "Unknown_build_option_0_5072": "'{0}' は不明なビルド オプションです。", "Unknown_build_option_0_Did_you_mean_1_5077": "'{0}' は不明なビルド オプションです。'{1}' を意図していましたか?", "Unknown_compiler_option_0_5023": "コンパイラ オプション '{0}' が不明です。", "Unknown_compiler_option_0_Did_you_mean_1_5025": "'{0}' は不明なコンパイラ オプションです。'{1}' を意図していましたか?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "不明なキーワードまたは識別子。'{0}' を意味していましたか?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "不明なオプション 'excludes' です。'exclude' ですか?", "Unknown_regular_expression_flag_1499": "正規表現フラグが不明です。", "Unknown_type_acquisition_option_0_17010": "不明な型の取得オプション '{0}'。", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "'{0}' は不明な型の取得オプションです。'{1}' を意図していましたか?", "Unknown_watch_option_0_5078": "'{0}' は不明な監視オプションです。", "Unknown_watch_option_0_Did_you_mean_1_5079": "'{0}' は不明な監視オプションです。'{1}' を意図していましたか?", "Unreachable_code_detected_7027": "到達できないコードが検出されました。", "Unterminated_Unicode_escape_sequence_1199": "未終了の Unicode エスケープ シーケンスです。", "Unterminated_quoted_string_in_response_file_0_6045": "応答ファイル '{0}' の文字列の終了引用符がありません。", "Unterminated_regular_expression_literal_1161": "未終了の正規表現リテラルです。", "Unterminated_string_literal_1002": "未終了の文字列リテラルです。", "Unterminated_template_literal_1160": "未終了のテンプレート リテラルです。", "Untyped_function_calls_may_not_accept_type_arguments_2347": "型指定のない関数の呼び出しで型引数を使用することはできません。", "Unused_label_7028": "未使用のラベル。", "Unused_ts_expect_error_directive_2578": "'@ts-expect-error' ディレクティブが使用されていません。", "Update_import_from_0_90058": "\"{0}\" からのインポートの更新", "Update_modifiers_of_0_90061": "'{0}' の修飾子を更新してください", "Updating_output_timestamps_of_project_0_6359": "プロジェクト '{0}' の出力タイムスタンプを更新しています...", "Updating_unchanged_output_timestamps_of_project_0_6371": "プロジェクト '{0}' の変更されていない出力タイムスタンプを更新しています...", "Use_0_95174": "\"{0}\" を使用します。", "Use_0_instead_5106": "代わりに '{0}' を使用してください。", "Use_Number_isNaN_in_all_conditions_95175": "すべての条件で 'Number.isNaN' を使用します。", "Use_element_access_for_0_95145": "'{0}' に要素アクセスを使用する", "Use_element_access_for_all_undeclared_properties_95146": "宣言されていないすべてのプロパティに対して要素アクセスを使用します。", "Use_import_type_95180": "'import type' を使用してください", "Use_synthetic_default_member_95016": "合成 'default' メンバーを使用します。", "Use_the_package_json_exports_field_when_resolving_package_imports_6408": "パッケージのインポートを解決する場合、package.json の 'exports' フィールドを使用してください。", "Use_the_package_json_imports_field_when_resolving_imports_6409": "インポートを解決するときに、package.json の 'imports' フィールドを使用してください。", "Use_type_0_95181": "'type {0}' を使用してください", "Using_0_subpath_1_with_target_2_6404": "'{0}' サブパス '{1}' をターゲット '{2}' と共に使用しています。", "Using_JSX_fragments_requires_fragment_factory_0_to_be_in_scope_but_it_could_not_be_found_2879": "JSX フラグメントを使用するには、フラグメント ファクトリ '{0}' がスコープ内に存在する必要がありますが、見つかりませんでした。", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "'for...of' ステートメントでの文字列の使用は ECMAScript 5 以上でのみサポートされています。", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "--build を使用すると、-b は tsc をコンパイラというよりビルド オーケストレーターのように動作させます。これは、複合プロジェクトのビルドをトリガーするために使用されます。詳細については、{0} を参照してください。", "Using_compiler_options_of_project_reference_redirect_0_6215": "Using compiler options of project reference redirect '{0}'.", "VERSION_6036": "バージョン", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "型 '{0}' の値には、型 '{1}' と共通のプロパティがありません。呼び出しますか?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "型 '{0}' の値は呼び出せません。'new' を含めますか?", "Variable_0_implicitly_has_an_1_type_7005": "変数 '{0}' の型は暗黙的に '{1}' になります。", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "変数 '{0}' の型は暗黙的に '{1}' になっていますが、使い方からより良い型を推論できます。", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "場所によっては、変数 '{0}' の型に '{1}' が暗黙的に指定されていますが、使い方からより良い型を推論できます。", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "変数 '{0}' は、型を決定できない一部の場所では、暗黙のうちに '{1}' 型になります。", "Variable_0_is_used_before_being_assigned_2454": "変数 '{0}' は割り当てられる前に使用されています。", "Variable_declaration_expected_1134": "変数の宣言が必要です。", "Variable_declaration_list_cannot_be_empty_1123": "変数宣言リストを空にすることはできません。", "Variable_declaration_not_allowed_at_this_location_1440": "変数の宣言はこの場所では許可されていません。", "Variable_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9010": "変数には、--isolatedDeclarations を含む明示的な型注釈が必要です。", "Variables_with_multiple_declarations_cannot_be_inlined_95186": "複数の宣言を含む変数をインライン化することはできません。", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "ソースの位置 {0} にある可変個引数要素は、ターゲットの位置 {1} にある要素と一致しません。", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "分散注釈は、オブジェクト、関数、コンストラクター、およびマップされた型の型エイリアスでのみサポートされています。", "Version_0_6029": "バージョン {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "このファイルの詳細については、https://aka.ms/tsconfig をご覧ください", "WATCH_OPTIONS_6918": "ウォッチ オプション", "Watch_and_Build_Modes_6250": "ウォッチ モードとビルド モード", "Watch_input_files_6005": "入力ファイルを監視します。", "Watch_option_0_requires_a_value_of_type_1_5080": "監視オプション '{0}' には型 {1} の値が必要です。", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "ここにパラメーター全体の型を追加することによってのみ、'{0}' の型を書き込むことができます。", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "関数を割り当てる際、パラメーターと戻り値が互換性のあるサブタイプであることを確認します。", "When_type_checking_take_into_account_null_and_undefined_6699": "型チェックを行うときは、'null' と 'undefined' が考慮されます。", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "画面をクリアする代わりに、古くなったコンソール出力をウォッチ モードで保持するかどうか。", "Wrap_all_invalid_characters_in_an_expression_container_95109": "式のコンテナー内のすべての無効な文字をラップする", "Wrap_all_invalid_decorator_expressions_in_parentheses_95195": "無効なデコレーター式をすべてかっこで囲む", "Wrap_all_object_literal_with_parentheses_95116": "すべてのオブジェクト リテラルをかっこで囲みます", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "親の設定が解除されたすべての JSX を JSX フラグメントでラップする", "Wrap_in_JSX_fragment_95120": "JSX フラグメントでラップする", "Wrap_in_parentheses_95194": "かっこで囲む", "Wrap_invalid_character_in_an_expression_container_95108": "式のコンテナー内の無効な文字をラップする", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "次の本体をかっこで囲みます。これはオブジェクト リテラルです", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "コンパイラ オプションの詳細については、{0} をご覧ください。", "You_cannot_rename_a_module_via_a_global_import_8031": "グローバル インポートを使用してモジュールの名前を変更することはできません。", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "'node_modules' フォルダーで定義されている要素の名前を変更することはできません。", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "別の 'node_modules' フォルダーで定義されている要素の名前を変更することはできません。", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "標準の TypeScript ライブラリで定義された要素の名前を変更することはできません。", "You_cannot_rename_this_element_8000": "この要素の名前を変更することはできません。", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "'{0}' は受け入れる引数が少なすぎるので、ここでデコレーターとして使用することができません。最初にこれを呼び出してから、'@{0}()' を書き込むつもりでしたか?", "_0_and_1_index_signatures_are_incompatible_2330": "'{0}' および '{1}' インデックス シグネチャに互換性がありません。", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "'{0}' および '{1}' 演算をかっこなしで混在させることはできません。", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "'{0}' は 2 回指定されています。'{0}' という名前の属性は上書きされます。", "_0_at_the_end_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17019": "型の末尾にある '{0}' は、有効な TypeScript 構文ではありません。'{1}' を書き込むつもりでしたか?", "_0_at_the_start_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17020": "型の先頭にある '{0}' は、有効な TypeScript 構文ではありません。'{1}' を書き込むつもりでしたか?", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "'{0}' をインポートするには、'esModuleInterop' フラグをオンにして既定のインポートを使用する必要があります。", "_0_can_only_be_imported_by_using_a_default_import_2595": "'{0}' をインポートするには、既定のインポートを使用する必要があります。", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "'{0}' をインポートするには、'require' 呼び出しを使用するか、'esModuleInterop' フラグをオンにして既定のインポートを使用する必要があります。", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "'{0}' をインポートするには、'require' 呼び出しを使用するか、既定のインポートを使用する必要があります。", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "'{0}' をインポートするには、'import {1} = require({2})' または既定のインポートを使用する必要があります。", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "'{0}' をインポートするには、'import {1} = require ({2})' を使用するか、'esModuleInterop' フラグをオンにして既定のインポートを使用する必要があります。", "_0_cannot_be_used_as_a_JSX_component_2786": "'{0}' を JSX コンポーネントとして使用することはできません。", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "'export type' を使用してエクスポートされたため、'{0}' は値として使用できません。", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "'import type' を使用してインポートされたため、'{0}' は値として使用できません。", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "'{0}' コンポーネントには、テキストを子要素として指定できません。JSX のテキストには 'string' 型が含まれていますが、'{1}' の予期された型は '{2}' です。", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "'{1}' に関連しない可能性のある任意の型で '{0}' をインスタンス化できます。", "_0_declarations_can_only_be_declared_inside_a_block_1156": "'{0}' 宣言は、ブロック内でのみ宣言できます。", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "'{0}' 宣言は TypeScript ファイルでのみ使用できます。", "_0_declarations_may_not_have_binding_patterns_1492": "'{0}' 宣言はバインド パターンを持っていない可能性があります。", "_0_declarations_must_be_initialized_1155": "'{0}' 宣言は初期化する必要があります。", "_0_expected_1005": "'{0}' が必要です。", "_0_has_a_string_type_but_must_have_syntactically_recognizable_string_syntax_when_isolatedModules_is__18055": "'{0}' には文字列型がありますが、'isolatedModules' が有効である場合、構文的に認識可能な文字列構文が必要です。", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "'{1}' という名前のエクスポートされたメンバーが '{0}' に含まれていません。候補: '{2}'", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "'{0}' の戻り値の型は暗黙的に '{1}' になっていますが、使い方からより良い型を推論できます。", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "'{0}' は、戻り値の型の注釈がなく、いずれかの return 式で直接的にまたは間接的に参照されているため、戻り値の型は暗黙的に 'any' になります。", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "'{0}' には型の注釈がなく、直接または間接的に初期化子で参照されているため、暗黙的に 'any' 型が含まれています。", "_0_index_signatures_are_incompatible_2634": "'{0}' インデックス シグネチャに互換性がありません。", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "'{0}'インデックス型'{1}' を '{2}'インデックス型'{3}' に割り当てることはできません。", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "'{0}' はプリミティブですが、'{1}' はラッパー オブジェクトです。できれば '{0}' をご使用ください。", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "'{0}' は型であるため、JavaScript ファイルにインポートできません。JSDoc 型の注釈で '{1}' を使用します。", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_verbatimModuleSyntax_is_enabled_1484": "'{0}' は型であり、'verbatimModuleSyntax' が有効であるときは、型のみのインポートを使用してインポートされる必要があります。", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "'{0}' は、'{1}' の未使用の名前変更です。型の注釈として使用するつもりでしたか?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "'{0}' は型 '{1}' の制約に代入できますが、'{1}' は制約 '{2}' の別のサブタイプでインスタンス化できることがあります。", "_0_is_automatically_exported_here_18044": "`{0}` は自動的にここにエクスポートされます。", "_0_is_declared_but_its_value_is_never_read_6133": "'{0}' が宣言されていますが、その値が読み取られることはありません。", "_0_is_declared_but_never_used_6196": "'{0}' は宣言されましたが使用されませんでした。", "_0_is_declared_here_2728": "'{0}' はここで宣言されています。", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "'{0}' はクラス '{1}' でプロパティとして定義されていますが、ここでは '{2}' でアクセサーとしてオーバーライドされています。", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "'{0}' はクラス '{1}' でアクセサーとして定義されていますが、ここではインスタンス プロパティとして '{2}' でオーバーライドされています。", "_0_is_deprecated_6385": "'{0}' は非推奨です。", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "'{0}' はキーワード '{1}' に関するメタプロパティとして無効です。候補: '{2}'。", "_0_is_not_allowed_as_a_parameter_name_1390": "'{0}' はパラメーター名として使用できません。", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "'{0}' は変数宣言の名前として使用できません。", "_0_is_of_type_unknown_18046": "'{0}''は 'unknown' 型です。", "_0_is_possibly_null_18047": "'{0}' は 'null' の可能性があります。", "_0_is_possibly_null_or_undefined_18049": "'{0}' は 'null' か 'undefined' の可能性があります。", "_0_is_possibly_undefined_18048": "'{0}' は 'undefined' の可能性があります。", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "'{0}' はそれ自身のベース式内で直接または間接的に参照されます。", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "'{0}' はそれ自身の型の注釈内で直接または間接的に参照されます。", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "'{0}' が複数回指定されているため、ここでの使用は上書きされます。", "_0_list_cannot_be_empty_1097": "'{0}' のリストを空にすることはできません。", "_0_modifier_already_seen_1030": "'{0}' 修飾子は既に存在します。", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "'{0}' 修飾子は、クラス、インターフェイス、または型エイリアスの型パラメーターでのみ使用できます", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_function_method_or_class_1277": "'{0}' 修飾子は、関数、メソッド、またはクラスの型パラメーターでのみ使用できます", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "'{0}' 修飾子はコンストラクター宣言では使用できません。", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "'{0}' 修飾子は、モジュールまたは名前空間の要素では使用できません。", "_0_modifier_cannot_appear_on_a_parameter_1090": "'{0}' 修飾子はパラメーターでは使用できません。", "_0_modifier_cannot_appear_on_a_type_member_1070": "'{0}' 修飾子は型メンバーでは使用できません。", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "'{0}' 修飾子は型パラメーターでは表示できません。", "_0_modifier_cannot_appear_on_a_using_declaration_1491": "'{0}' 修飾子を 'using' 宣言で使用することはできません。", "_0_modifier_cannot_appear_on_an_await_using_declaration_1495": "'{0}' 修飾子を 'await using' 宣言で使用することはできません。", "_0_modifier_cannot_appear_on_an_index_signature_1071": "'{0}' 修飾子はインデックス シグネチャでは使用できません。", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "'{0}' 修飾子はこの種類のクラス要素では使用できません。", "_0_modifier_cannot_be_used_here_1042": "'{0}' 修飾子はここでは使用できません。", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "'{0}' 修飾子は環境コンテキストでは使用できません。", "_0_modifier_cannot_be_used_with_1_modifier_1243": "'{0}' 修飾子と '{1}' 修飾子は同時に使用できません。", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "'{0}' 修飾子を private 識別子とともに使用することはできません。", "_0_modifier_must_precede_1_modifier_1029": "'{0}' 修飾子は '{1}' 修飾子の前に指定する必要があります。", "_0_must_be_followed_by_a_Unicode_property_value_expression_enclosed_in_braces_1531": "'\\{0}' の後には、中かっこで囲まれた Unicode プロパティ値式を指定する必要があります。", "_0_needs_an_explicit_type_annotation_2782": "'{0}' には、明示的な型の注釈が必要です。", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "'{0}' は型のみを参照しますが、ここで名前空間として使用されています。", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "'{0}' は型のみを参照しますが、ここで値として使用されています。", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "'{0}' は型を参照しているだけですが、こちらでは値として使用されています。'{0} 内の {1}' を使用しますか?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "'{0}' は型のみを参照しますが、ここでは値として使用されています。ターゲット ライブラリを変更しますか? 'lib' コンパイラ オプションを es2015 以降に変更してみてください。", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "'{0}' は UMD グローバルを参照していますが、現在のファイルはモジュールです。代わりにインポートを追加することを考慮してください。", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "'{0}' は値を参照していますが、ここでは型として使用されています。'typeof {0}' を意図していましたか?", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1291": "'{0}' は型に解決され、'{1}' が有効であるときに再エクスポートする前に、このファイル内で型のみとしてマークする必要があります。'{0}' がインポートされる場所で 'import type' を使用することを検討してください。", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1292": "'{0}' は型に解決され、'{1}' が有効であるときに再エクスポートする前に、このファイル内で型のみとしてマークする必要があります。'export type { {0} as default }' を使用することを検討してください。", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_verbatimMo_1485": "'{0}' は型のみの宣言に解決されるため、'verbatimModuleSyntax' が有効であるときは、型のみのインポートを使用してインポートされる必要があります。", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1289": "'{0}' は型のみの宣言に解決され、'{1}' が有効であるときに再エクスポートする前に、このファイル内で型のみとしてマークする必要があります。'{0}' がインポートされる場所で 'import type' を使用することを検討してください。", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1290": "'{0}' は型のみの宣言に解決され、'{1}' が有効であるときに再エクスポートする前に、このファイル内で型のみとしてマークする必要があります。'export type { {0} as default }' を使用することを検討してください。", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_1_is_1448": "\"{0}\" は型のみの宣言に解決されるため、'{1}' が有効であるときは、型のみの再エクスポートを使用して再エクスポートされる必要があります。", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "config json ファイル の 'compilerOptions' オブジェクト内に '{0}' を設定する必要があります。", "_0_tag_already_specified_1223": "'{0}' タグは既に指定されています。", "_0_was_also_declared_here_6203": "ここでは '{0}' も宣言されました。", "_0_was_exported_here_1377": "ここでは '{0} ' がエクスポートされました。", "_0_was_imported_here_1376": "ここでは '{0}' がインポートされました。", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "'{0}' には戻り値の型の注釈がないため、戻り値の型は暗黙的に '{1}' になります。", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "'{0}' には戻り値の型の注釈がないため、yield 型は暗黙的に '{1}' になります。", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "'abstract' 修飾子は、クラス宣言、メソッド宣言、またはプロパティ宣言のみに使用できます。", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "'accessor' 修飾子は、プロパティ宣言でのみ使用できます。", "and_here_6204": "およびここで。", "arguments_cannot_be_referenced_in_property_initializers_2815": "プロパティ初期化子で 'arguments' を参照することはできません。", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "\"auto\": インポート、エクスポート、import.meta、jsx (jsx: react-jsx を使用)、または esm 形式 (モジュール: node16+) でファイルをモジュールとして扱います。", "await_expression_cannot_be_used_inside_a_class_static_block_18037": "'await' 式はクラスの静的ブロック内では使用できません。", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "ファイルがモジュールの場合、'await' 式はそのファイルのトップ レベルでのみ使用できますが、このファイルにはインポートもエクスポートも含まれていません。空の 'export {}' を追加して、このファイルをモジュールにすることを検討してください。", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "'await' 式は、非同期関数内と、モジュールのトップ レベルでのみ許可されます。", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "'await' 式は、パラメーター初期化子では使用できません。", "await_has_no_effect_on_the_type_of_this_expression_80007": "'await' は、この式の型に対しては効果がありません。", "await_using_statements_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_th_2853": "'await using' ステートメントは、ファイルがモジュールである場合、そのファイルのトップ レベルでのみ使用できますが、このファイルにはインポートもエクスポートも含まれていません。空の 'export {}' を追加して、このファイルをモジュールにすることを検討してください。", "await_using_statements_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_2852": "'await using' ステートメントは、非同期関数内と、モジュールのトップ レベルでのみ許可されます。", "await_using_statements_cannot_be_used_inside_a_class_static_block_18054": "'await using' ステートメントをクラスの静的ブロック内で使用することはできません。", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "'baseUrl' オプションは '{0}' に設定され、この値を使用して非相対モジュール名 '{1}' を解決します。", "c_must_be_followed_by_an_ASCII_letter_1512": "'\\c' の後には ASCII 文字が続く必要があります。", "can_only_be_used_at_the_start_of_a_file_18026": "'#!' は、ファイルの先頭でのみ使用できます。", "case_or_default_expected_1130": "'case' または 'default' が必要です。", "catch_or_finally_expected_1472": "'catch' または 'finally' が必要です。", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "'const' 列挙型メンバーの初期化子が、無限値に評価されました。", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "'const' 列挙型メンバーの初期化子が、許可されない値 'NaN' に評価されました。", "const_enum_member_initializers_must_be_constant_expressions_2474": "const 列挙型メンバー初期化子は定数式である必要があります。", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "'const' 列挙型は、プロパティまたはインデックスのアクセス式、インポート宣言またはエクスポートの代入の右辺、型のクエリにのみ使用できます。", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "'constructor' をパラメーターのプロパティ名として使用することはできません。", "constructor_is_a_reserved_word_18012": "'#constructor' は予約語です。", "default_Colon_6903": "既定:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "厳格モードでは 'delete' を識別子で呼び出すことはできません。", "export_Asterisk_does_not_re_export_a_default_1195": "'export *' では既定のものは再エクスポートされません。", "export_can_only_be_used_in_TypeScript_files_8003": "'export =' は、TypeScript ファイルでのみ使用できます。", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "環境モジュールとモジュール拡張は常に表示されるので、これらに 'export' 修飾子を適用することはできません。", "extends_clause_already_seen_1172": "'extends' 句は既に存在します。", "extends_clause_must_precede_implements_clause_1173": "extends' 句は 'implements' 句の前に指定しなければなりません。", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "エクスポートされたクラス '{0}' の 'extends' 句がプライベート名 '{1}' を持っているか、使用しています。", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "エクスポートされたクラスの 'extends' 句がプライベート名 '{0}' を持っているか、使用しています。", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "エクスポートされたインターフェイス '{0}' の 'extends' 句がプライベート名 '{1}' を持っているか、使用しています。", "false_unless_composite_is_set_6906": "'composite' が設定されていない場合は 'false'", "false_unless_strict_is_set_6905": "'strict' が設定されている場合を除き、' false '", "file_6025": "ファイル", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "ファイルがモジュールの場合、'for await' ループはそのファイルのトップ レベルでのみ使用できますが、このファイルにはインポートもエクスポートも含まれていません。空の 'export {}' を追加して、このファイルをモジュールにすることを検討してください。", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "'for await' ループは、非同期関数内と、モジュールのトップ レベルでのみ許可されます。", "for_await_loops_cannot_be_used_inside_a_class_static_block_18038": "'for await' ループは、クラスの静的ブロック内では使用できません。", "get_and_set_accessors_cannot_declare_this_parameters_2784": "'get' および 'set' アクセサーでは 'this' パラメーターを宣言できません。", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "'files' が指定されている場合は '[]'、それ以外の場合は '[\"**/*\"]5D;'", "implements_clause_already_seen_1175": "'implements' 句は既に存在します。", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "'implements' 句は、TypeScript ファイルでのみ使用できます。", "import_can_only_be_used_in_TypeScript_files_8002": "'import ... =' は、TypeScript ファイルでのみ使用できます。", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "'infer' 宣言は、条件付き型の 'extends' 句でのみ許可されます。", "k_must_be_followed_by_a_capturing_group_name_enclosed_in_angle_brackets_1510": "'\\k' の後には、山かっこで囲まれたキャプチャ グループ名を指定する必要があります。", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "'let' は、'let' 宣言または 'const' 宣言で名前として使用することはできません。", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "モジュール === 'AMD'、'UMD'、'System'、'ES6'、'Classic'、それ以外の場合は 'Node'", "module_system_or_esModuleInterop_6904": "module === \"system\" or esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "ターゲットにコンストラクト シグネチャがない 'new' 式の型は、暗黙的に 'any' になります。", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "`[\"node_modules\", \"bower_components\", \"jspm_packages\"]`' と、指定されている場合は 'outDir' の値を加算します。", "one_of_Colon_6900": "次のいずれか:", "one_or_more_Colon_6901": "1 つ以上", "options_6024": "オプション", "or_JSX_element_expected_1145": "'{' または JSX 要素が必要です。", "or_expected_1144": "'{' または ';' が必要です。", "package_json_does_not_have_a_0_field_6100": "'package.json' に '{0}' フィールドがありません。", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "'package.json' には、バージョン '{0}' と一致する 'typesVersions' エントリがありません。", "package_json_had_a_falsy_0_field_6220": "'package.json' には、false に評価される '{0}' フィールドが含まれています。", "package_json_has_0_field_1_that_references_2_6101": "'package.json' に '{2}' を参照する '{0}' フィールド '{1}' があります。", "package_json_has_a_peerDependencies_field_6281": "'package.json' に 'peerDependencies' フィールドがあります。", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "'package.json' には、有効な semver の範囲ではない 'typesVersions' エントリ '{0}' が含まれています。", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "'package.json' には、コンパイラ バージョン '{1}' に一致する 'typesVersions' エントリ '{0}' が含まれていて、モジュール名 '{2}' に一致するパターンを探しています。", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "'package.json' には、バージョン固有のパス マッピングを含む 'typesVersions' フィールドが含まれています。", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "package.json のスコープ '{0}' は、指定子 '{1}' を明示的に null にマッピングします。", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "package.json のスコープ '{0}' は、指定子 '{1}' のターゲットに無効な型です。", "package_json_scope_0_has_no_imports_defined_6273": "package.json のスコープ '{0}' にはインポートが定義されていません。", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "'paths' オプションが指定され、モジュール名 '{0}' と一致するパターンを検索します。", "q_is_only_available_inside_character_class_1511": "'\\q' は文字クラス内でのみ使用できます。", "q_must_be_followed_by_string_alternatives_enclosed_in_braces_1521": "'\\q' の後には、中かっこで囲まれた代替文字列を指定する必要があります。", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "'readonly' 修飾子はプロパティ宣言またはインデックス シグネチャのみに使用できます。", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "'readonly' 型の修飾子は、配列およびタプル リテラル型でのみ使用できます。", "require_call_may_be_converted_to_an_import_80005": "'require' の呼び出しはインポートに変換される可能性があります。", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "`resolution-mode` は、型のみのインポートに対してのみ設定できます。", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "`resolution-mode` は、型インポート アサーションの唯一の有効なキーです。", "resolution_mode_is_the_only_valid_key_for_type_import_attributes_1463": "'resolution-mode' が、型インポート属性の唯一の有効なキーです。", "resolution_mode_should_be_either_require_or_import_1453": "\"resolution-mode\" は \"require\" または \"import\" のいずれかにする必要があります。", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "'rootDirs' オプションが設定され、このオプションを使用して相対モジュール名 '{0}' を解決します。", "super_can_only_be_referenced_in_a_derived_class_2335": "'super' は、派生クラスでのみ参照できます。", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "'super' は、派生クラスのメンバーまたはオブジェクトのリテラル式のメンバーでのみ参照されます。", "super_cannot_be_referenced_in_a_computed_property_name_2466": "'super' は、計算されたプロパティ名では参照できません。", "super_cannot_be_referenced_in_constructor_arguments_2336": "'super' はコンストラクター引数では参照できません。", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "オプション 'target' が 'ES2015' 以降の場合、'super' はオブジェクトのリテラル式のメンバーでのみ使用できます。", "super_may_not_use_type_arguments_2754": "'super' では型引数を使用できません。", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "派生クラスのコンストラクター内の 'super' のプロパティにアクセスする前に、'super' を呼び出す必要があります。", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "派生クラスのコンストラクター内の 'this' にアクセスする前に、'super' を呼び出す必要があります。", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "'super' の後には、引数リストまたはメンバー アクセスが必要です。", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "'super' プロパティ アクセスはコンストラクター、メンバー関数、または派生クラスのメンバー アクセサーでのみ許可されます。", "this_cannot_be_referenced_in_a_computed_property_name_2465": "'this' は、計算されたプロパティ名では参照できません。", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "'this' はモジュール本体内または名前空間本体内では参照できません。", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "'this' は、静的プロパティ初期化子では参照できません。", "this_cannot_be_referenced_in_current_location_2332": "'this' は現在の場所では参照できません。", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "'this' は型として注釈を持たないため、暗黙的に型 'any' になります。", "true_for_ES2022_and_above_including_ESNext_6930": "ESNext を含む ES2022 以降の場合は 'true' です。", "true_if_composite_false_otherwise_6909": "'composite' の場合は 'true'、それ以外の場合は 'false'", "true_when_moduleResolution_is_node16_nodenext_or_bundler_otherwise_false_6411": "'moduleResolution' が 'node16'、'nodenext'、または 'bundler' である場合は 'true'、それ以外の場合は 'false' です。", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: TypeScript コンパイラ", "type_Colon_6902": "種類:", "unique_symbol_types_are_not_allowed_here_1335": "'unique symbol' 型はここでは許可されていません。", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "'unique symbol' 型は変数ステートメントの変数でのみ許可されています。", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "'unique symbol' 型は、バインディング名を持つ変数の宣言では使用できません。", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "'use strict' ディレクティブは、複雑なパラメーター リストでは使用できません。", "use_strict_directive_used_here_1349": "'use strict' ディレクティブがここで使用されています。", "with_statements_are_not_allowed_in_an_async_function_block_1300": "'with' 式は、非同期関数ブロックでは使用できません。", "with_statements_are_not_allowed_in_strict_mode_1101": "厳格モードでは 'with' ステートメントは使用できません。", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "'yield' 式は、それを含むジェネレーターに戻り値の型の注釈がないため、暗黙的に 'any' 型になります。", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "'yield' 式は、パラメーター初期化子では使用できません。"}