plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'host.exp.exponent'
version = '16.1.10'

android {
  namespace "expo.modules.camera"
  defaultConfig {
    versionCode 32
    versionName "16.1.10"
  }
}

dependencies {
  def camerax_version = "1.4.1"

  api "androidx.exifinterface:exifinterface:1.3.7"
  api "androidx.appcompat:appcompat:1.1.0"

  implementation "androidx.camera:camera-core:${camerax_version}"
  implementation "androidx.camera:camera-camera2:${camerax_version}"
  implementation "androidx.camera:camera-lifecycle:${camerax_version}"
  implementation "androidx.camera:camera-video:${camerax_version}"
  implementation "com.google.android.gms:play-services-code-scanner:16.1.0"

  implementation "androidx.camera:camera-view:${camerax_version}"
  implementation "androidx.camera:camera-extensions:${camerax_version}"
  implementation "com.google.mlkit:barcode-scanning:17.3.0"
  implementation "androidx.camera:camera-mlkit-vision:${camerax_version}"
}
