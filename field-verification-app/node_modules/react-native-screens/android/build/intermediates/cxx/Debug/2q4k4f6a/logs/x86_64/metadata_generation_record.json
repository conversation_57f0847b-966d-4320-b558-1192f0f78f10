[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64/android_gradle_build.json due to:", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/usr/local/Cellar/openjdk@17/17.0.15/libexec/openjdk.jdk/Contents/Home/bin/java \\\n  --class-path \\\n  /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar \\\n  com.google.prefab.cli.AppKt \\\n  --build-system \\\n  cmake \\\n  --platform \\\n  android \\\n  --abi \\\n  x86_64 \\\n  --os-version \\\n  24 \\\n  --stl \\\n  c++_shared \\\n  --ndk-version \\\n  27 \\\n  --output \\\n  /var/folders/42/zjc2_yrd70x0yjkdtpbtl2980000gp/T/agp-prefab-staging12131929867279099714/staged-cli-output \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab\n", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64'", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64'", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=x86_64 \\\n  -DCMAKE_ANDROID_ARCH_ABI=x86_64 \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/2q4k4f6a/obj/x86_64 \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/2q4k4f6a/obj/x86_64 \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/prefab/x86_64/prefab \\\n  -B/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64 \\\n  -GNinja \\\n  -DANDROID_STL=c++_shared \\\n  -DRNS_NEW_ARCH_ENABLED=true \\\n  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\n", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=x86_64 \\\n  -DCMAKE_ANDROID_ARCH_ABI=x86_64 \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/2q4k4f6a/obj/x86_64 \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/2q4k4f6a/obj/x86_64 \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/prefab/x86_64/prefab \\\n  -B/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64 \\\n  -GNinja \\\n  -DANDROID_STL=c++_shared \\\n  -DRNS_NEW_ARCH_ENABLED=true \\\n  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\n", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of /Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64/compile_commands.json.bin normally", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked /Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64/compile_commands.json to /Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/tools/debug/x86_64/compile_commands.json", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]