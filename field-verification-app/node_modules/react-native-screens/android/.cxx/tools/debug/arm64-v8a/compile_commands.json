[{"directory": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -I/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/../cpp -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles/rnscreens.dir/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o -c /Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp", "file": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp"}, {"directory": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -I/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/../cpp -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles/rnscreens.dir/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o -c /Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp", "file": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp"}, {"directory": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -I/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/../cpp -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o -c /Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/src/main/cpp/jni-adapter.cpp", "file": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/src/main/cpp/jni-adapter.cpp"}, {"directory": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -I/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/../cpp -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o -c /Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/src/main/cpp/NativeProxy.cpp", "file": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/src/main/cpp/NativeProxy.cpp"}, {"directory": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -I/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/../cpp -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o -c /Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/src/main/cpp/OnLoad.cpp", "file": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/src/main/cpp/OnLoad.cpp"}]