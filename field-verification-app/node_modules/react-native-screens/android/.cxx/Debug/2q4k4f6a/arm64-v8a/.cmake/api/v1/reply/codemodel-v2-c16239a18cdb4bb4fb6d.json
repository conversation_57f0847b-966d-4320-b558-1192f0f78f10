{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "rnscreens", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "rnscreens::@6890427a1f51a3e7e1df", "jsonFile": "target-rnscreens-Debug-ebe004aca5b7471e6466.json", "name": "rnscreens", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/arm64-v8a", "source": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android"}, "version": {"major": 2, "minor": 3}}