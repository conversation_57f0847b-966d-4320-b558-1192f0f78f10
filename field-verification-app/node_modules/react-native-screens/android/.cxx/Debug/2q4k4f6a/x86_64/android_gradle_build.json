{"buildFiles": ["/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake", "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake", "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfig.cmake", "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake", "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "rnscreens", "output": "/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/2q4k4f6a/obj/x86_64/librnscreens.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so", "/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}