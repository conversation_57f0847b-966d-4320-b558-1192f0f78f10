                        -H/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-24
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/2q4k4f6a/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/2q4k4f6a/obj/x86_64
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/prefab/x86_64/prefab
-B/Users/<USER>/field_pro/field-verification-app/node_modules/react-native-screens/android/.cxx/Debug/2q4k4f6a/x86_64
-GNinja
-DANDROID_STL=c++_shared
-DRNS_NEW_ARCH_ENABLED=true
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2